package structs

import (
	"sort"
	"train/base/event"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Resonance struct {
	Slots []*ResonanceSlot `bson:"slots"` // 被共鸣的槽位
	plr   *Player          `bson:"-"`
}

type ResonanceSlot struct {
	Id  int     `bson:"id"` // 被共鸣的角色
	plr *Player `bson:"-"`
}

func (this *ResonanceSlot) ToPb() *pb.ResonanceSlot {
	return &pb.ResonanceSlot{
		Id: cast.ToInt32(this.Id),
	}
}

func NewResonance() *Resonance {
	return &Resonance{}
}

func (this *Resonance) Init(plr *Player) {
	this.plr = plr
	this.Slots = lo.Filter(this.Slots, func(item *ResonanceSlot, _ int) bool {
		return item.Id != 0
	})
	for _, slot := range this.Slots {
		slot.plr = plr
	}

	plr.eventCenter.On(event.PassengerLevelUp, func(i ...interface{}) { this.CheckUnset() })
	plr.eventCenter.On(event.PassengerNew, func(i ...interface{}) { this.CheckUnset() })
	plr.eventCenter.On(event.PassengerReset, func(i ...interface{}) { this.CheckUnset() })
	plr.eventCenter.On(event.PassengerStarLvUp, func(i ...interface{}) { this.CheckUnset() })
	plr.eventCenter.On(event.PassengerTrans, func(i ...interface{}) { this.CheckUnset() })
}

func (this *Resonance) ToPb() *pb.Resonance {
	return &pb.Resonance{
		Slots: lo.Map(this.Slots, func(e *ResonanceSlot, i int) *pb.ResonanceSlot { return e.ToPb() }),
	}
}

func (this *Resonance) Add(id int) {
	this.Slots = append(this.Slots, &ResonanceSlot{plr: this.plr, Id: id})
}

func (this *Resonance) Remove(id int) {
	this.Slots = array.RemoveBy(this.Slots, func(e *ResonanceSlot) bool { return e.Id == id })
}

func (this *Resonance) GetSlot(index int) *ResonanceSlot {
	if index >= len(this.Slots) {
		return nil
	}
	return this.Slots[index]
}

func (this *Resonance) IsBeResonated(id int) bool {
	return this.GetSlotById(id) != nil
}

func (this *Resonance) GetSlotById(id int) *ResonanceSlot {
	return array.Find(this.Slots, func(slot *ResonanceSlot) bool { return slot.Id == id })
}

func (this *Resonance) CheckUnset() {
	passengers := this.GetResonanceRoles()
	for _, p := range passengers {
		slot := this.GetSlotById(p.Id)
		if slot != nil {
			this.Remove(p.Id)
		}
	}
}

func (this *Resonance) GetResonanceRoles() []*Passenger {
	passengers := array.SliceCopy(this.plr.Passenger)
	sort.Slice(passengers, func(i, j int) bool {
		if passengers[i].Level != passengers[j].Level {
			return passengers[i].Level > passengers[j].Level
		}
		if passengers[i].StarLv != passengers[j].StarLv {
			return passengers[i].StarLv > passengers[j].StarLv
		}
		return passengers[i].GetJson().SortId < passengers[j].GetJson().SortId
	})
	return array.Slice(passengers, 0, 5)
}

func (this *Resonance) GetResonanceLv() int {
	passengers := this.GetResonanceRoles()
	if len(passengers) < 5 {
		return 0
	}
	p := lo.MinBy(passengers, func(a *Passenger, b *Passenger) bool {
		return a.Level < b.Level
	})
	return p.Level
}

func (this *Resonance) GetResonanceEquipInfo(roles []*Passenger) map[int]*EquipItem {
	equipInfo := make(map[int]*EquipItem)
	for index := 1; index <= 3; index++ {
		for _, role := range roles {
			equip := role.GetEquip(index)
			if equip == nil {
				equipInfo[index] = nil
				break
			}

			lv := equip.GetEffectsLevel()
			if existingEquip, ok := equipInfo[index]; !ok {
				equipInfo[index] = equip
			} else {
				orgLv := existingEquip.GetEffectsLevel()
				if orgLv > lv {
					equipInfo[index] = equip
				} else if orgLv == lv && existingEquip.Id < equip.Id {
					equipInfo[index] = equip
				}
			}
		}
	}
	return equipInfo
}

func (this *Resonance) GetResonanceTalentInfo(roles []*Passenger) map[int]int {
	talentInfo := make(map[int]int)
	for id := 1; id <= 5; id++ {
		for _, role := range roles {
			lv := role.GetTalentLevel(id)
			if _, exists := talentInfo[id]; !exists {
				talentInfo[id] = lv
			} else {
				talentInfo[id] = ut.Min(talentInfo[id], lv)
			}

			if talentInfo[id] <= 0 {
				break
			}
		}
	}
	return talentInfo
}

func (r *Resonance) IsActive() bool {
	return r.GetResonanceLv() > 0
}

// IsCanOperate 判断是否能操作升级之类的
//
// Parameters:
//   - id int
//
// Returns:
//   - bool
func (r *Resonance) IsCanOperate(passenger *Passenger) bool {
	if !r.IsActive() {
		return true
	}
	return r.IsResonance(passenger.Id)
}

func (r *Resonance) IsResonance(roleId int) bool {
	roles := r.GetResonanceRoles()
	return array.Some(roles, func(role *Passenger) bool { return role.Id == roleId })
}
