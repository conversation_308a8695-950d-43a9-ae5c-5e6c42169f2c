package structs

import (
	"context"
	"train/common/pb"
	"train/db"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
)

type PvpNormalData struct {
	Id     string        `bson:"id"`     // 玩家id
	Roles  []*BattleRole `bson:"roles"`  // 阵容数据
	Rivals []string      `bson:"rivals"` // 对手
	Score  float64       `bson:"score"`  // 积分
}

func (p *PvpNormalData) ToPb() *pb.PvpNormalData {
	if p == nil {
		return nil
	}
	return &pb.PvpNormalData{
		BattleRoles: lo.Map(p.Roles, func(item *BattleRole, index int) *pb.BattleRole { return item.ToPb() }),
		Score:       int32(p.Score),
	}
}

// 获取普通竞技场数据
func (p *Player) GetNormalPvpData() (*PvpNormalData, int) {
	// if !p.IsUnlockFunction(function_type.PLAY_PVP_1) {
	// 	return nil, -1
	// }
	data := p.normalPvp
	if data == nil {
		serializer := ut.NewRedisHashSerializer(db.GetRedis())
		data = &PvpNormalData{Id: p.Id}
		err := serializer.HGetStruct(context.TODO(), db.RKPvpNormalDataOf(p.Id), data)
		if err != nil {
			if err == redis.Nil {
				// 数据不存在，返回空结构体
				return data, -1
			}
			log.Error("[%s]获取普通竞技场数据失败:%s", p.Id, err.Error())
			return nil, -1
		}
	}

	r := db.GetRedis().ZRevRank(context.TODO(), db.RKPvpNormalRank(p.ServerId), p.Id)
	if r.Err() != nil {
		db.GetRedis().ZAdd(context.TODO(), db.RKPvpNormalRank(p.ServerId), redis.Z{
			Score:  data.Score + ut.TimeScore(),
			Member: p.Id,
		})
		r = db.GetRedis().ZRevRank(context.TODO(), db.RKPvpNormalRank(p.ServerId), p.Id)
		if r.Err() != nil {
			log.Error("获取竞技场排名出错:%s", p.Id)
			return nil, -1
		}
	}
	return data, int(r.Val()) + 1
}

func (p *Player) UpdateNormalPvpRoles(ids []int) bool {
	roles := make([]*BattleRole, 0)
	for _, id := range ids {
		passenger := p.GetPassengerById(int(id))
		if passenger == nil {
			log.Error("更新普通竞技场防守阵容失败: 找不到乘客ID %d", id)
			return false
		}
		roles = append(roles, passenger.ToBattleRole())
	}
	serializer := ut.NewRedisHashSerializer(db.GetRedis())
	e := serializer.HSetField(context.TODO(), db.RKPvpNormalDataOf(p.Id), "roles", roles)
	if e != nil {
		log.Error("更新普通竞技场防守阵容失败:%s", e.Error())
		return false
	}
	// 设置初始分数
	cmd := db.GetRedis().HExists(context.Background(), db.RKPvpNormalDataOf(p.Id), "score")
	if !cmd.Val() {
		score := 1000 + ut.TimeScore()
		serializer.HSetField(context.TODO(), db.RKPvpNormalDataOf(p.Id), "score", score)
		// 更新排行榜
		db.GetRedis().ZAdd(context.Background(), db.RKPvpNormalRank(p.ServerId), redis.Z{
			Score:  score,
			Member: p.Id,
		})
	}
	return true
}
