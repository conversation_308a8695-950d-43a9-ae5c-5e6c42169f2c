package structs

import (
	"sort"
	"strings"
	"train/base/cfg"
	"train/base/enum/time_stone_record_type"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type JackpotHistory struct {
	id      int `bson:""`
	rewards []int
	time    int
}

// func (this *Player) AddJackpotHistory() (int, []int32) {
// }

func NewEmptyJackpotMod() *JackpotMod {
	return &JackpotMod{
		JackpotHistory: make(map[string][]*Condition),
	}
}

type QualityComeData struct {
	Quality int  `bson:"quality"` // 品质
	Time    int  `bson:"time"`    // 当前累计次数
	IsGet   bool `bson:"isGet"`   // 是否提前获取了
}

type JackpotMod struct {
	JackpotDailyNum   int                     `bson:"jackpotDailyNum"`   // 今日抽卡次数
	JackpotTotalCount int                     `bson:"jackpotTotalCount"` // 总抽卡次数
	JackpotPoints     int                     `bson:"jackpotPoints"`     // 抽卡积分 用于领取累计奖励
	JackpotHistory    map[string][]*Condition `bson:"JackpotHistory"`    // 抽卡记录
}

func (j *JackpotMod) ToPb() *pb.Jackpot {
	return &pb.Jackpot{
		JackpotDailyNum:   int32(j.JackpotDailyNum),
		JackpotTotalCount: int32(j.JackpotTotalCount),
		JackpotPoints:     int32(j.JackpotPoints),
	}
}

// OnJackpot
/*
 * @description 抽卡后处理
 * @param cnt
 */
func (plr *Player) OnJackpot() {
	plr.JackpotMod.JackpotDailyNum += 1
	plr.JackpotMod.JackpotTotalCount += 1
}

// RestJackpotDailyCount
/*
 * @description 重置每日抽卡次数
 */
func (plr *Player) RestJackpotDailyCount() {
	plr.JackpotMod.JackpotDailyNum = 0
}

func (plr *Player) GetJackpotHistory() map[string][]*Condition {
	return plr.JackpotMod.JackpotHistory
}

func (plr *Player) SetJackpotHistory(his map[string][]*Condition) {
	plr.JackpotMod.JackpotHistory = his
}

func (plr *Player) Jackpot(count int) []*Condition {
	misc := cfg.GetMisc()
	// 抽卡结果
	result := make([]*Condition, 0)

	// 必出乘客
	mustGetCharacter := misc.Jackpot.MustGetCharacter
	mustGetCnt := len(mustGetCharacter)
	for i := 0; i < count; i++ {
		datas := cfg.CharacterContainer.GetData()
		// 必出列表
		jackPotCount := plr.JackpotMod.JackpotTotalCount
		if jackPotCount < mustGetCnt {
			mustId := mustGetCharacter[jackPotCount]
			ids := []int{}
			if str, ok := mustId.(string); ok {
				ids = ut.ToInt(strings.Split(str, ","))
			} else if id, ok := mustId.(float64); ok {
				ids = append(ids, cast.ToInt(id))
			}
			datas = lo.Filter(datas, func(character *cfg.Character[int], i int) bool {
				return array.Has(ids, character.Id)
			})
		} else if misc.Jackpot.DiffNum > jackPotCount {
			// 必出未解锁
			datas = lo.Filter(datas, func(character *cfg.Character[int], i int) bool {
				return plr.GetPassengerById(character.Id) == nil && !array.Some(result, func(r *Condition) bool { return cast.ToInt(r.Id) == character.Id })
			})
		}
		plr.DoTimeStoneRecord(time_stone_record_type.Jackpot)

		// 增加抽卡次数
		plr.OnJackpot()
		random := plr.RandomPassenger(datas...)
		cr := plr.CheckChangePassenger(random, result)
		result = append(result, cr)
		plr.GrantReward(cr, ta.ResChangeSceneTypeJackpot)
	}
	return result
}

func (plr *Player) TestJackpot(count int) []*cfg.Character[int] {
	// misc := cfg.GetMisc()
	// 抽卡结果
	result := make([]*cfg.Character[int], 0)

	// 必出乘客
	// mustGetCharacter := misc.Jackpot.MustGetCharacter
	// mustGetCnt := len(mustGetCharacter)
	for i := 0; i < count; i++ {
		datas := cfg.CharacterContainer.GetData()
		// // 必出列表
		// jackPotCount := plr.JackpotMod.JackpotTotalCount
		// if jackPotCount < mustGetCnt {
		// 	mustId := mustGetCharacter[jackPotCount]
		// 	ids := []int{}
		// 	if str, ok := mustId.(string); ok {
		// 		ids = ut.ToInt(strings.Split(str, ","))
		// 	} else if id, ok := mustId.(float64); ok {
		// 		ids = append(ids, cast.ToInt(id))
		// 	}
		// 	datas = lo.Filter(datas, func(character *cfg.Character, i int) bool {
		// 		return array.Has(ids, character.Id)
		// 	})
		// } else if misc.Jackpot.DiffNum > jackPotCount {
		// 	// 必出未解锁
		// 	datas = lo.Filter(datas, func(character *cfg.Character, i int) bool {
		// 		return plr.GetPassengerById(character.Id) == nil && !array.Some(result, func(r *Condition) bool { return cast.ToInt(r.Id) == character.Id })
		// 	})
		// }
		random := plr.RandomPassenger(datas...)
		result = append(result, random)
	}
	return result
}

func (plr *Player) TestJackpot2(count int) []*cfg.Character[int] {
	misc := cfg.GetMisc()
	// 抽卡结果
	result := make([]*cfg.Character[int], 0)

	// 必出乘客
	mustGetCharacter := misc.Jackpot.MustGetCharacter
	mustGetCnt := len(mustGetCharacter)
	for i := 0; i < count; i++ {
		datas := cfg.CharacterContainer.GetData()
		// // 必出列表
		jackPotCount := plr.JackpotMod.JackpotTotalCount
		if jackPotCount < mustGetCnt {
			mustId := mustGetCharacter[jackPotCount]
			ids := []int{}
			if str, ok := mustId.(string); ok {
				ids = ut.ToInt(strings.Split(str, ","))
			} else if id, ok := mustId.(float64); ok {
				ids = append(ids, cast.ToInt(id))
			}
			datas = lo.Filter(datas, func(character *cfg.Character[int], i int) bool {
				return array.Has(ids, character.Id)
			})
		} else if misc.Jackpot.DiffNum > jackPotCount {
			// 必出未解锁
			datas = lo.Filter(datas, func(character *cfg.Character[int], i int) bool {
				return plr.GetPassengerById(character.Id) == nil && !array.Some(result, func(r *cfg.Character[int]) bool { return cast.ToInt(r.Id) == character.Id })
			})
		}
		plr.OnJackpot()
		random := plr.RandomPassenger(datas...)
		result = append(result, random)
	}
	return result
}

func (plr *Player) CalcAvgStarLv() {
	testCount := 100000
	jackCount := 300

	misc := cfg.GetMisc()
	FragUp := misc.FragUp

	calcAvgStar := func(Map map[int]int, index int) (float64, float64) {
		res := make([]int, 0)
		for id, v := range Map {
			bean, _ := cfg.CharacterContainer.GetBeanById(id)
			q := bean.Quality
			starLv := cfg.GetStarLvByQuality(q)
			vv := v
			for {
				bean, _ := cfg.StarUpContainer.GetBeanById(starLv)
				needFrag := bean.UpCost
				trans := 1
				for i := q; i < bean.Quality; i++ {
					trans *= FragUp[i-1]
				}

				need := needFrag * trans
				if vv-need > 0 {
					vv -= need
					starLv++
				} else {
					break
				}
			}
			res = append(res, starLv)
			// log.Debug("%d抽 %v count: %d, star: %v", index, id, v, starLv)
		}
		sort.Slice(res, func(i, j int) bool {
			return res[i] > res[j]
		})
		sum := lo.Reduce(res, func(agg int, item int, _ int) int {
			return item + agg
		}, 0)
		avg := ut.Float64(sum) / ut.Float64(len(res))

		res = array.Slice(res, 0, 5)
		sum2 := lo.Reduce(res, func(agg int, item int, _ int) int {
			return item + agg
		}, 0)
		topAvg := ut.Float64(sum2) / ut.Float64(len(res))
		// log.Debug("%v", res)
		return avg, topAvg
	}

	res := make([][]*cfg.Character[int], 0)
	for i := 0; i < testCount; i++ {
		plr.JackpotMod.JackpotTotalCount = 1
		roles := plr.TestJackpot2(jackCount)
		res = append(res, roles)
	}

	starMap := make(map[int]map[int]int)

	for i := 0; i < jackCount; i++ {
		sum := 0.0
		sumTop := 0.0
		for j, r := range res {
			if starMap[j] == nil {
				starMap[j] = make(map[int]int)
				starMap[j][1005] = 1
				starMap[j][1006] = 1
				starMap[j][1007] = 1
			}
			id := r[i].Id
			starMap[j][id]++
			avg, topAvg := calcAvgStar(starMap[j], i+1)
			sum += avg
			sumTop += topAvg
		}
		avg := sum / float64(len(res))
		topAvg := sumTop / float64(len(res))
		log.Debug("第%v抽: %v %v", i+1, avg, topAvg)
	}
}
