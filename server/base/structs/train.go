package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum"
	"train/base/enum/build_attr"
	"train/base/event"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type Train struct {
	Head                   *Carriage   `bson:"head"`
	Carriages              []*Carriage `bson:"carriages"`
	ElectricTime           int         `bson:"electricTime"`           //电能增益剩余时间，这里只用于给前端返回剩余时间，真正的计算下放到各个车厢
	LastUpdateElectricTime int         `bson:"lastUpdateElectricTime"` //上次更新电能增益的时间
	WaterTime              int         `bson:"waterTime"`              //水能增益剩余时间，这里只用于给前端返回剩余时间，真正的计算下放到各个乘客
	LastUpdateWaterTime    int         `bson:"lastUpdateWaterTime"`    //上次更新水能增益的时间
	plr                    *Player     `bson:"-"`
}

func NewTrainData(plr *Player) *Train {
	headId := 1015
	datas := cfg.TrainContainer.GetData()
	carriages := make([]*Carriage, 0)
	for _, data := range datas {
		if data.SortId == 0 && data.Id != headId {
			carriage := NewCarriage(data.Id, plr)
			carriages = append(carriages, carriage)
		}
	}
	return &Train{
		Head:      NewCarriage(headId, plr),
		Carriages: carriages,
	}

}

func (this *Train) Init(plr *Player) {
	this.plr = plr
	this.Head.Init(plr)
	for _, carriage := range this.Carriages {
		carriage.Init(plr)
	}
}

// Count 车厢数量
func (this *Train) Count() int {
	return len(this.Carriages)
}

// ToTrainInfo 车厢数据转pb前端数据
func (this *Train) ToTrainInfo() *pb.TrainInfo {
	return &pb.TrainInfo{
		Head: this.Head.ToPb(),
		Carriages: lo.Map(this.Carriages, func(carriage *Carriage, i int) *pb.CarriageInfo {
			return carriage.ToPb()
		}),
		ElectricTime: int32(this.GetElectricTime()),
		WaterTime:    int32(this.GetWaterTime()),
	}
}

func (this *Train) AddElectricTime(time int) {
	this.updateElectricTime()
	this.ElectricTime += time
	for _, carriage := range this.Carriages {
		carriage.ElectricTime += time
	}
}

func (this *Train) AddWaterTime(time int) {
	this.updateWaterTime()
	this.WaterTime += time
	for _, passenger := range this.plr.Passenger {
		passenger.WaterTime += time
	}
}

func (this *Train) updateElectricTime() {
	now := this.plr.GetNowTime()
	if this.ElectricTime > 0 {
		passTime := ut.Min(this.ElectricTime, now-this.LastUpdateElectricTime)
		this.ElectricTime -= passTime
	}
	this.LastUpdateElectricTime = now
}

func (this *Train) updateWaterTime() {
	now := this.plr.GetNowTime()
	if this.WaterTime > 0 {
		passTime := ut.Min(this.WaterTime, now-this.LastUpdateWaterTime)
		this.WaterTime -= passTime
	}
	this.LastUpdateWaterTime = now
}

func (this *Train) GetElectricTime() int {
	this.updateElectricTime()
	return this.ElectricTime
}

func (this *Train) GetWaterTime() int {
	this.updateWaterTime()
	return this.WaterTime
}

func (this *Train) CheckAttrByCond(cond *Condition) bool {
	if cond.Type == condition.STAR_DUST {
		return true
	}
	return array.Some(this.Carriages, func(c *Carriage) bool { return c.GetAttrByCond(cond) > 0 })
}

func (this *Train) GetAttrByCond(cond *Condition) int {
	return lo.Reduce(this.Carriages, func(sum int, c *Carriage, i int) int { return c.GetAttrByCond(cond) + sum }, 0)
}

func (this *Train) GetAttr(attr string) int {
	return lo.Reduce(this.Carriages, func(sum int, c *Carriage, i int) int { return c.GetAttr(attr) + sum }, 0)
}

func (this *Train) GetLoad() int {
	return lo.Reduce(this.Carriages, func(sum int, c *Carriage, i int) int { return c.GetLoad() + sum }, 0)
}

func (t *Train) GetCarriageById(id int) *Carriage {
	if t.Head.Id == id {
		return t.Head
	}
	carriage, _ := lo.Find(t.Carriages, func(carriage *Carriage) bool {
		return carriage.Id == id
	})
	return carriage
}

func GetTrainItemByTrainId(trainId, themeId int) []*cfg.TrainItem[string] {
	datas := cfg.TrainItemContainer.GetData()
	list := lo.Filter(datas, func(data *cfg.TrainItem[string], i int) bool {
		return data.IsBelongTrain(trainId, themeId)
	})
	return list
}

type CarriageOutPut struct {
	Val      float64 `bson:"val,omitempty"`      //当前累计
	AccTotal int     `bson:"accTotal,omitempty"` //总累计产出
}

func (this *CarriageOutPut) ToPb() *pb.CarriageOutput {
	return &pb.CarriageOutput{
		Val:      int32(this.Val),
		AccTotal: int64(this.AccTotal),
	}
}

// Carriage 车厢
type Carriage struct {
	Id                   int             `bson:"id"`                             // 车厢id
	Builds               []*Build        `bson:"builds"`                         // 建设主题
	ThemeLv              int             `bson:"themeLv"`                        //主题等级
	UnlockTime           int             `bson:"unlockTime"`                     // 解锁时间
	BuildEndTime         int             `bson:"buildEndTime"`                   // 建造结束时间
	StarOutput           *CarriageOutPut `bson:"starOutput,omitempty"`           // 星尘产出
	HeartOutput          *CarriageOutPut `bson:"heartOutput,omitempty"`          // 爱心产出
	ElectricOutput       *CarriageOutPut `bson:"electricOutput,omitempty"`       // 电力产出
	WaterOutput          *CarriageOutPut `bson:"waterOutput,omitempty"`          // 水产出
	VitalityOutput       *CarriageOutPut `bson:"vitalityOutput,omitempty"`       // 元气值产出
	LastUpdateOutputTime int             `bson:"lastUpdateOutputTime,omitempty"` //上次更新产出的时间
	OutputTime           int             `bson:"outputTime,omitempty"`           //累计时间
	json                 *cfg.Train[int] `bson:"-"`
	OpenDoor             bool            `bson:"openDoor"`     //是否打开猫猫门
	Goods                []*Goods        `bson:"goods"`        // 货物数据
	ElectricTime         int             `bson:"electricTime"` //电力增益剩余时间，和自然流逝的时间同步计算
	plr                  *Player         `bson:"-"`
}

func NewCarriage(id int, plr *Player) *Carriage {
	carriageData, _ := cfg.TrainContainer.GetBeanById(id)
	now := plr.GetNowTime()
	carriage := &Carriage{
		Id:                   id,
		UnlockTime:           now,
		BuildEndTime:         now + carriageData.CostTime*ut.TIME_SECOND,
		plr:                  plr,
		ThemeLv:              1,
		LastUpdateOutputTime: now,
	}

	if carriageData.CostTime <= 0 {
		carriage.OpenDoor = true
	}
	carriage.Init(plr)
	return carriage
}

// CheckOutputObj
/*
 * @description 检查产出对象是否为空
 * @param obj
 * @return *CarriageOutPut
 */
func CheckOutputObj(obj *CarriageOutPut) *CarriageOutPut {
	if obj == nil {
		obj = &CarriageOutPut{}
	}
	return obj
}

func (this *Carriage) Init(plr *Player) {
	this.plr = plr
	this.StarOutput = CheckOutputObj(this.StarOutput)
	this.HeartOutput = CheckOutputObj(this.HeartOutput)
	this.ElectricOutput = CheckOutputObj(this.ElectricOutput)
	this.WaterOutput = CheckOutputObj(this.WaterOutput)
	this.VitalityOutput = CheckOutputObj(this.VitalityOutput)
	this.unlockDefaultGoods()
	for _, build := range this.Builds {
		build.Init(this)
	}
	this.unlockDefaultBuilds()
}

func (this *Carriage) unlockDefaultBuilds() {
	beans := cfg.TrainItemContainer.GetData()
	beans = lo.Filter(beans, func(b *cfg.TrainItem[string], i int) bool { return b.CarriageId == this.Id && b.IsDefaultUnlock() })
	for _, bean := range beans {
		this.UnlockBuild(bean.Order)
	}
}

func (this *Carriage) unlockDefaultGoods() {
	beans := cfg.TrainGoodsContainer.GetData()
	beans = lo.Filter(beans, func(bean *cfg.TrainGoods[string], i int) bool { return bean.TrainId == this.Id && bean.SortId == 0 })
	for _, bean := range beans {
		this.UnlockGoods(bean.Id, 1)
	}
}

func (this *Carriage) GetJson() *cfg.Train[int] {
	if this.json == nil {
		json, _ := cfg.TrainContainer.GetBeanById(this.Id)
		this.json = json
	}
	return this.json
}

func (this *Carriage) GetRoleCnt() int {
	bean := this.GetJson()
	cnt := bean.RoleCnt

	themeLv := this.ThemeLv

	themes := cfg.TrainThemeContainer.GetData()
	themes = lo.Filter(themes, func(t *cfg.TrainTheme[string], i int) bool { return t.CarriageId == this.Id && t.Order <= themeLv })
	isMaxLv := this.isAllBuildsMaxLv(themeLv)
	for _, theme := range themes {
		if theme.Order < themeLv {
			cnt += theme.RoleCnt
		} else if isMaxLv {
			cnt += theme.RoleCnt
		}
	}

	cnt += this.plr.Tech.GetCarriageRoleCnt(this.Id)
	return cnt
}

func (this *Carriage) isAllBuildsMaxLv(themeLv int) bool {
	max := this.GetThemeUnlockLevel(themeLv)
	if max == 0 {
		return false
	}
	beans := cfg.TrainItemContainer.GetData()
	beans = lo.Filter(beans, func(b *cfg.TrainItem[string], i int) bool {
		return b.CarriageId == this.Id && b.Skin == 1 && b.Show == 1
	})
	for _, bean := range beans {
		build := this.GetBuildByOrder(bean.Order)
		if build == nil || !build.IsMaxLv(max) {
			return false
		}
	}
	return true
}

func (this *Carriage) GetThemeUnlockLevel(themeLv int) int {
	themes := cfg.TrainThemeContainer.GetData()
	theme := array.Find(themes, func(t *cfg.TrainTheme[string]) bool {
		return t.CarriageId == this.Id && t.Order == themeLv
	})
	if theme == nil {
		return 0
	}
	return theme.UnlockLevel
}

// ToPb 转pb数据
func (this *Carriage) ToPb() *pb.CarriageInfo {
	return &pb.CarriageInfo{
		Id:      cast.ToInt32(this.Id),
		ThemeLv: cast.ToInt32(this.ThemeLv),
		Builds: lo.Map(this.Builds, func(build *Build, i int) *pb.TrainItemInfo {
			return build.ToPb()
		}),
		BuildTime:      cast.ToInt32(this.GetBuildSurplusTime()),
		OpenDoor:       this.OpenDoor,
		StarOutput:     this.StarOutput.ToPb(),
		HeartOutput:    this.HeartOutput.ToPb(),
		ElectricOutput: this.ElectricOutput.ToPb(),
		WaterOutput:    this.WaterOutput.ToPb(),
		VitalityOutput: this.VitalityOutput.ToPb(),
		Goods:          lo.Map(this.Goods, func(good *Goods, i int) *pb.CarriageGoodsInfo { return good.ToPb() }),
		OutputTime:     cast.ToInt32(this.OutputTime),
	}
}

func (this *Carriage) GetBuildById(id string) *Build {
	bean, _ := cfg.TrainItemContainer.GetBeanByUnique(id)
	order := bean.Order
	return this.GetBuildByOrder(order)
}

func (this *Carriage) GetBuildByOrder(order int) *Build {
	return array.Find(this.Builds, func(b *Build) bool { return b.Order == order })
}

func (this *Carriage) GetAbsAttr(att string) int {
	sum := 0
	// 建设产出
	for _, build := range this.Builds {
		sum += build.GetAttr(att)
	}

	themes := lo.Filter(cfg.TrainThemeContainer.GetData(), func(t *cfg.TrainTheme[string], i int) bool { return t.CarriageId == this.Id && t.Order <= this.ThemeLv })
	for _, theme := range themes {
		if theme.Order == this.ThemeLv && !this.isAllBuildsMaxLv(this.ThemeLv) {
			continue
		}
		sum += cast.ToInt(theme.Add[att])
	}
	sum += this.plr.Tech.GetCarriageAttr(this.Id, att)
	return sum
}

func (this *Carriage) GetPercentAttr(att string) float64 {
	return this.plr.Tech.GetCarriageAttrPercent(this.Id, att)
}

func (this *Carriage) IsBuilt() bool {
	return this.GetBuildSurplusTime() <= 0
}

func (this *Carriage) GetBuildSurplusTime() int {
	now := this.plr.GetNowTime()
	return ut.Max(0, this.GetBuildEndTime()-now)
}

func (this *Carriage) GetBuildEndTime() int {
	return this.BuildEndTime
}

func (this *Carriage) SetBuiltSpeedUp(speedUpTime int) {
	this.BuildEndTime -= speedUpTime
}

func (this *Carriage) SetBuiltDone() {
	this.BuildEndTime = this.plr.GetNowTime()
	this.OpenDoor = true
}

func (this *Carriage) GetOutputObjByCond(item *Condition) *CarriageOutPut {
	switch item.Type {
	case condition.STAR_DUST:
		return this.StarOutput
	case condition.HEART:
		return this.HeartOutput
	case condition.PROP:
		if item.Id == item_id.ELECTRIC {
			return this.ElectricOutput
		}
		if item.Id == item_id.WATER {
			return this.WaterOutput
		}
		if item.Id == item_id.Vitality {
			return this.VitalityOutput
		}
	}
	return nil
}

func (this *Carriage) UnlockWork(index int) {
}

func (this *Carriage) IsUnlockWork(index int) bool {
	return this.GetWorkCnt() >= index
}

func (this *Carriage) GetWorkCnt() int {
	cnt := this.GetJson().WorkCnt
	themeLv := this.ThemeLv

	themes := cfg.TrainThemeContainer.GetData()
	themes = lo.Filter(themes, func(t *cfg.TrainTheme[string], i int) bool { return t.CarriageId == this.Id && t.Order <= themeLv })
	isMaxLv := this.isAllBuildsMaxLv(themeLv)
	for _, theme := range themes {
		if theme.Order < themeLv {
			cnt += theme.WorkCnt
		} else if isMaxLv {
			cnt += theme.WorkCnt
		}
	}
	cnt += this.plr.Tech.GetCarriageRoleCnt(this.Id)
	return cnt
}

func (this *Carriage) GetWorkers() []*Passenger {
	passengers := lo.Filter(this.plr.Passenger, func(p *Passenger, i int) bool { return p.WorkId == this.Id })
	return passengers
}

func (this *Carriage) GetCheckIns() []*Passenger {
	return lo.Filter(this.plr.Passenger, func(p *Passenger, i int) bool {
		return p.DormId == this.Id
	})
}

func (this *Carriage) BuildLvUp(order int) {
	plr := this.plr

	build := this.GetBuildByOrder(order)
	if build == nil {
		this.UnlockBuild(order)
	} else {
		// plr.UpdateCarriageOutput(this) //先结算一下产出
		build.lvUp()
		plr.eventCenter.Emit(event.LevelUpBuild, build)
		log.Info("[%s] 设施升级: %d-%d-%d", plr.GetUid(), this.Id, order, build.Lv)
	}

	//下一主题解锁没消耗的话，设施全部满级时自动解锁
	bean, _ := cfg.TrainThemeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Id, this.ThemeLv+1))
	if bean != nil && this.isAllBuildsMaxLv(this.ThemeLv) {
		plr.PushNew(enum.MARKNEW_BUILD_UNLOCK_SKIN, []int{this.Id})
	}
}

func (this *Carriage) UnlockBuild(order int) {
	build := this.GetBuildByOrder(order)
	if build != nil {
		return
	}

	plr := this.plr
	// plr.UpdateCarriageOutput(this) //先结算一下产出
	build = NewBuild(order, this)
	id := fmt.Sprintf("%d-%d-%d", this.Id, order, build.Lv)
	this.Builds = append(this.Builds, build)
	plr.eventCenter.Emit(event.UnLockBuild, id)
	plr.eventCenter.Emit(event.LevelUpBuild, build) //设施解锁也算成升级
	log.Info("[%s] 解锁设施: %d-%d", plr.GetUid(), this.Id, order)
}

func (this *Carriage) ThemeLvUp() {
	this.ThemeLv++
}

func (this *Carriage) IsMaxThemeLv() bool {
	nextLv := this.ThemeLv + 1
	bean, _ := cfg.TrainThemeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", this.Id, nextLv))
	return bean != nil
}

func (this *Carriage) GetAttr(attr string) int {
	if !this.IsBuilt() {
		return 0
	}
	inc := 0.0
	if this.IsWork() {
		inc = this.GetWorkOutputRate(attr)
	} else if this.IsDorm() {
		inc = this.GetCheckInOutputRate(attr)
	} else if this.IsVitality() {
		for _, good := range this.Goods {
			inc += float64(good.GetAttr())
		}
	}
	sum := ut.Floor(this.getAttr(attr) + inc)
	return sum
}

func (this *Carriage) IsWork() bool {
	return this.GetJson().Type == 1
}

func (this *Carriage) IsDorm() bool {
	return this.GetJson().Type == 0
}

func (this *Carriage) IsVitality() bool {
	return this.GetJson().Type == 2
}

// 排除掉入住增益
func (this *Carriage) getAttr(attr string) float64 {
	abs := this.GetAbsAttr(attr)
	perInc := this.GetPercentAttr(attr)
	sum := float64(abs) * (1 + perInc)
	return sum
}

func (this *Carriage) GetCheckInOutputRate(attr string) float64 {
	if !this.IsBuilt() {
		return 0
	}
	abs := this.GetAbsAttr(attr)
	misc := cfg.Misc_CContainer.GetObj()
	rate := 0.0
	for _, role := range this.GetCheckIns() {
		if role.GetCheckInBuildAttr() == attr {
			rate += misc.CheckInOutputRate
		}
	}
	sum := float64(abs) * rate
	return sum
}

func (this *Carriage) GetWorkOutputRate(attr string) float64 {
	if !this.IsBuilt() {
		return 0
	}
	abs := this.GetAbsAttr(attr)
	misc := cfg.Misc_CContainer.GetObj()
	rate := float64(len(this.GetWorkers())) * misc.WorkOutputRate
	sum := float64(abs) * rate
	return sum
}

func (this *Carriage) GetAttrByCond(cond *Condition) int {
	attr := TransCondToBuildAttr(cond)
	return this.GetAttr(attr)
}

func (this *Carriage) GetLoad() int {
	return this.GetJson().Load
}

// TrainItem 车厢主题的设施
type Build struct {
	Order      int                    `bson:"order"`      //设施序号
	UnlockTime int                    `bson:"unlockTime"` // 解锁时间
	Lv         int                    `bson:"lv"`         //等级
	Skin       int                    `bson:"skin"`       //皮肤
	json       *cfg.TrainItem[string] `bson:"-"`          //配置
	carriage   *Carriage              `bson:"-"`
}

func NewBuild(order int, carriage *Carriage) *Build {
	plr := carriage.plr
	build := &Build{Order: order, UnlockTime: plr.GetNowTime(), Lv: 1, Skin: 1}
	build.Init(carriage)
	return build
}

func (this *Build) Init(carriage *Carriage) {
	this.carriage = carriage
}

func (this *Build) lvUp() {
	preSkinLv := this.GetAttr(build_attr.SKIN)

	this.Lv++

	curSkinLv := this.GetAttr(build_attr.SKIN)
	if preSkinLv != curSkinLv {
		this.ChangeSkin(curSkinLv)
	}
}

func (this *Build) GetCarriageOrder() string {
	return cast.ToString(this.carriage.Id) + "-" + cast.ToString(this.Order)
}

func (this *Build) IsMaxLv(maxNum int) bool {
	return this.Lv >= maxNum
}

func (this *Build) ToPb() *pb.TrainItemInfo {
	return &pb.TrainItemInfo{
		Order: cast.ToInt32(this.Order),
		Lv:    cast.ToInt32(this.Lv),
		Skin:  cast.ToInt32(this.Skin),
	}
}

func (this *Build) GetAttr(att string) int {
	beans := cfg.TrainItemLevelContainer.GetData()
	carriageId := this.carriage.Id
	beans = lo.Filter(beans, func(b *cfg.TrainItemLevel[string], i int) bool {
		return b.CarriageId == carriageId && b.Order == this.Order && b.Lv <= this.Lv
	})
	sum := 0
	for _, bean := range beans {
		sum += cast.ToInt(bean.Add[att])
	}
	if att == build_attr.SKIN {
		sum += 1
	}
	return sum
}

func (this *Build) ChangeSkin(skin int) {
	this.Skin = skin
}

func (plr *Player) GetCarriageById(id int) *Carriage {
	return plr.Train.GetCarriageById(id)
}

func (plr *Player) isCarriageOverBuilt(id int) bool {
	carriage := plr.GetCarriageById(id)
	return carriage != nil && carriage.IsBuilt()
}

func (plr *Player) UnlockCarriage(id int) *Carriage {
	carriage := plr.GetCarriageById(id)
	if carriage != nil {
		return carriage
	}
	carriage = NewCarriage(id, plr)
	plr.addCarriage(carriage)
	carriage.ElectricTime = ut.Max(0, plr.Train.GetElectricTime()-carriage.GetBuildSurplusTime())
	plr.eventCenter.Emit(event.CreateNewCarriage, carriage)
	log.Info("[%s] 解锁车厢: %d", plr.GetUid(), id)
	return carriage
}

func (plr *Player) IsUnlockBuildById(buildId string) bool {
	bean, _ := cfg.TrainItemContainer.GetBeanByUnique(buildId)
	if bean == nil {
		return false
	}
	carriage := plr.GetCarriageById(bean.CarriageId)
	return carriage.GetBuildByOrder(bean.Order) != nil
}

func (plr *Player) UnlockBuildById(buildId string) {
	bean, _ := cfg.TrainItemContainer.GetBeanByUnique(buildId)
	if bean == nil {
		return
	}
	carriage := plr.GetCarriageById(bean.CarriageId)
	carriage.UnlockBuild(bean.Order)
}

// PushTrain 添加解锁的车厢
func (plr *Player) addCarriage(carriage *Carriage) {
	plr.Train.Carriages = append(plr.Train.Carriages, carriage)
}

// 更新设施产出
func (plr *Player) UpdateCarriageOutput(carriage *Carriage, during int) {
	if !plr.isRealTime() || during <= 0 {
		return
	}
	lastUpdateOutputTime := ut.Max(carriage.GetBuildEndTime(), carriage.LastUpdateOutputTime) //上次更新时间从建设完成的时间开始算

	now := plr.GetNowTime()
	if lastUpdateOutputTime >= now {
		return
	}
	passTime := ut.Min(during, now-lastUpdateOutputTime)
	carriage.LastUpdateOutputTime = now
	plr.updateCarriageOutputByPassTime(carriage, passTime)
}

func (plr *Player) updateCarriageOutputByPassTime(carriage *Carriage, passTime int) {
	carriage.OutputTime += passTime
	rate := 1.0
	time := TransToRealTime(ut.TIME_HOUR)
	count := ut.Floor(float64(carriage.OutputTime) / float64(time))
	if count <= 0 {
		return
	}
	plr.updateCarriageOutput(carriage, count, rate)
	carriage.OutputTime -= count * time
}

func (plr *Player) updateCarriageOutput(carriage *Carriage, count int, rate float64) {
	if carriage.IsWork() {
		plr.UpdateCarriageElectric(carriage, count, rate)
		plr.UpdateCarriageWater(carriage, count, rate)
	} else if carriage.IsVitality() {
		plr.UpdateCarriageVitality(carriage, count, rate)
	}
	plr.UpdateCarriageStar(carriage, count, rate)
	plr.UpdateCarriageHeart(carriage, count, rate)
}

func (plr *Player) UpdateCarriageOutputByCond(carriage *Carriage, count int, rate float64, item *Condition) {
	if !carriage.IsBuilt() {
		return
	}
	outputRate := carriage.GetAttrByCond(item)
	if outputRate <= 0 {
		return
	}

	outputObj := carriage.GetOutputObjByCond(item)
	output := ut.Float64(outputRate*count) * rate

	if item.Type == condition.STAR_DUST {
		attr := carriage.getAttr(TransCondToBuildAttr(item))
		carriageOutput := ut.Float64(attr) / ut.Float64(outputRate) * output
		checkInOutput := output - carriageOutput
		if checkInOutput > 0 {
			output = carriageOutput
			plr.PassengerStarOutput += checkInOutput
			log.Debug("[%s] UpdatePassengerStarOutput [%d] [%d,%d], count: %d, rate: %f, outputRate: %d, add: %f， output: %f", plr.GetUid(), carriage.Id, item.Type, cast.ToInt(item.Id), count, rate, 0, checkInOutput, plr.PassengerStarOutput)
		}
	}

	outputObj.Val += output
	log.Debug("[%s] UpdateCarriageOutput [%d] [%d,%d], count: %d, rate: %f, outputRate: %d, add: %f， output: %f", plr.GetUid(), carriage.Id, item.Type, cast.ToInt(item.Id), count, rate, outputRate, output, outputObj.Val)
}

// UpdateCarriageStar 星尘
func (plr *Player) UpdateCarriageStar(carriage *Carriage, count int, rate float64) {
	plr.UpdateCarriageOutputByCond(carriage, count, rate, &Condition{Type: condition.STAR_DUST})
}

// UpdateCarriageHeart 爱心
func (plr *Player) UpdateCarriageHeart(carriage *Carriage, count int, rate float64) {
	plr.UpdateCarriageOutputByCond(carriage, count, rate, &Condition{Type: condition.HEART})
}

// UpdateCarriageElectric 电
func (plr *Player) UpdateCarriageElectric(carriage *Carriage, count int, rate float64) {
	plr.UpdateCarriageOutputByCond(carriage, count, rate, &Condition{Type: condition.PROP, Id: item_id.ELECTRIC})
}

// UpdateCarriageWater 水
func (plr *Player) UpdateCarriageWater(carriage *Carriage, count int, rate float64) {
	plr.UpdateCarriageOutputByCond(carriage, count, rate, &Condition{Type: condition.PROP, Id: item_id.WATER})
}

// UpdateCarriageVitality 元气值
func (plr *Player) UpdateCarriageVitality(carriage *Carriage, count int, rate float64) {
	plr.UpdateCarriageOutputByCond(carriage, count, rate, &Condition{Type: condition.PROP, Id: item_id.Vitality})
}

func (plr *Player) UpdateCarriageOutputBySpeedUp(carriage *Carriage, passTime int) {
	plr.updateCarriageOutputByPassTime(carriage, passTime)
}

// 领取车厢星尘产出
func (plr *Player) ClaimCarriageOutput(carriage *Carriage, item *Condition) int {
	outputObj := carriage.GetOutputObjByCond(item)
	output := cast.ToInt(outputObj.Val)
	num := item.Num
	if num > output || num <= 0 {
		num = output
	}
	outputObj.Val -= cast.ToFloat64(num)
	outputObj.AccTotal += num
	item.Num = num
	num = plr.GrantReward(item, ta.ResChangeSceneTypeTrainPickUp)
	log.Info("[%s]:领取车厢: %d, 领取资源: [%d,%d], 数量：%d, 剩余: %f", plr.GetUid(), carriage.Id, item.Type, cast.ToInt(item.Id), item.Num, outputObj.Val)
	return num
}

// CheckReward
/*
 * @description 刷出奖励时，检查车厢产出是否满足条件 （策划要求通常是，车厢不产出x资源，则不可以刷出x资源类型的奖励）
 * @param cond
 * @return bool
 */
func (this *Train) CheckReward(cond *Condition) bool {
	if cond.Type == condition.STAR_DUST {
		return true
	}
	if cond.Type == condition.Chest {
		chestBean, _ := lo.Find(cfg.ChestContainer.GetData(), func(chest *cfg.Chest[int]) bool { return chest.Id == cast.ToInt(cond.Id) })
		if chestBean == nil {
			return false
		}
		return array.Some(chestBean.Reward, func(reward *cfg.ChestReward) bool {
			return this.CheckAttrByCond(&Condition{Type: reward.Type, Id: reward.Id})
		})
	}
	return array.Some(this.Carriages, func(c *Carriage) bool { return c.GetAttrByCond(cond) > 0 })
}

func (this *Train) CheckRewards(conds []*Condition) []*Condition {
	return lo.Filter(conds, func(c *Condition, i int) bool { return this.CheckReward(c) })
}

func (this *Train) AddOutputTime(time int) {
	for _, carriage := range this.Carriages {
		carriage.OutputTime += time
	}
	this.plr.UpdateAllOutput()
}
