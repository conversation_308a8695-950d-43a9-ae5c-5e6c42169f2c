package structs

import (
	"context"
	"fmt"
	"math"
	"train/base/cfg"
	"train/base/enum"
	"train/base/enum/equip_effect_target"
	"train/base/enum/equip_effect_type"
	comm "train/common"
	"train/db"
	ut "train/utils"
	"train/utils/array"

	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
)

var start = 35
var every = 5

var levelWeight = []int{70, 25, 5}
var talentWeight = []int{5, 25, 70}

func genRobot(sid int) {
	robotsPerLevel := 5
	totalCount := (100 - start + 1) * robotsPerLevel
	successCount := 0

	log.Debug("开始生成机器人数据，区服:%d，等级范围:%d-%d，每等级%d个，总数:%d", sid, start, 100, robotsPerLevel, totalCount)

	for i := start; i <= 100; i++ {
		log.Debug("正在生成等级 %d 的机器人", i)

		// 每个等级生成5个机器人
		for robotIndex := 0; robotIndex < robotsPerLevel; robotIndex++ {
			ary := make([]*Passenger, 0)
			// 抽卡
			v := monitorJackpot(i)
			// 取五个乘客
			for j := 0; j < every; j++ {
				idx := ut.RandomIndexByWeight(v, func(d *Passenger) int { return int(math.Pow(float64(d.StarLv), 0.5) * 10) })
				tmp := v[idx]
				ary = append(ary, tmp)
				v = array.RemoveIndex(v, idx)
				// 等级
				tmp.Level = i + ut.RandomIndexByWeight(levelWeight, func(d int) int { return d })
			}

			// 配置
			data := cfg.RobotEquipContainer.GetData()
			var r *cfg.RobotEquip[int]
			for _, v := range data {
				if i >= v.Id {
					r = v
				}
			}
			if r != nil {
				r = data[0]
			}

			// 生成天赋等级
			genTalent(ary, i, r)
			// 装备
			genEquip(ary, i, r)

			// 保存机器人 - 这里会有详细的错误日志
			if saveRobot(ary, i, sid) {
				successCount++
			} else {
				log.Error("机器人生成失败，等级: %d，第%d个", i, robotIndex+1)
			}
		}

		// 每10个等级输出一次进度
		if (i-start+1)%10 == 0 {
			log.Debug("已生成 %d/%d 个等级，成功机器人数: %d", i-start+1, 100-start+1, successCount)
		}
	}
	log.Debug("机器人数据创建完成！！！成功生成 %d/%d 个机器人", successCount, totalCount)
}

func TestRobot() {
	go func() {
		sid := 2
		// 清理旧数据
		if comm.IsDebug() {
			cleanOldRobotData(sid)
		}
		// 测试添加到2区
		genRobot(sid)
	}()
}

// cleanOldRobotData 清理旧的机器人数据
func cleanOldRobotData(sid int) {
	log.Debug("开始清理区服 %d 的旧机器人数据", sid)
	// 1. 获取机器人集合数据
	robotIds := db.GetRedis().SMembers(context.TODO(), db.RKRobotMember(sid))
	if robotIds.Err() != nil {
		log.Error("获取机器人集合失败: %s", robotIds.Err().Error())
		return
	}
	robotIdList := robotIds.Val()
	if len(robotIdList) == 0 {
		log.Debug("区服 %d 没有旧的机器人数据需要清理", sid)
		return
	}
	log.Debug("找到 %d 个机器人需要清理", len(robotIdList))
	// 2. 遍历每个机器人ID，删除相关数据
	for _, robotId := range robotIdList {
		// 删除排行榜数据
		err := db.GetRedis().ZRem(context.TODO(), db.RKPvpNormalRank(sid), robotId).Err()
		if err != nil {
			log.Error("删除机器人 %s 排行榜数据失败: %s", robotId, err.Error())
		}
		// 删除竞技场数据
		err = db.GetRedis().Del(context.TODO(), db.RKPvpNormalDataOf(robotId)).Err()
		if err != nil {
			log.Error("删除机器人 %s 竞技场数据失败: %s", robotId, err.Error())
		}
		// 删除基础信息数据
		err = db.GetRedis().Del(context.TODO(), db.RKPlayerBaseInfo(robotId)).Err()
		if err != nil {
			log.Error("删除机器人 %s 基础信息失败: %s", robotId, err.Error())
		}
	}
	// 3. 清空机器人集合
	err := db.GetRedis().Del(context.TODO(), db.RKRobotMember(sid)).Err()
	if err != nil {
		log.Error("清空机器人集合失败: %s", err.Error())
	} else {
		log.Debug("成功清理区服 %d 的 %d 个机器人数据", sid, len(robotIdList))
	}
}

func saveRobot(passengers []*Passenger, stepLv, sid int) bool {
	// 注意 这里没有共鸣的说法
	roles := lo.Map(passengers, func(item *Passenger, index int) *BattleRole {
		return &BattleRole{
			Id:      item.Id,
			Lv:      item.Level,
			StarLv:  item.StarLv,
			Talents: item.Talents,
			Equips:  item.Equips,
		}
	})
	if len(roles) <= 0 {
		log.Error("saveRobot - roles is empty")
		return false
	}
	// 生成机器人基本数据 - 使用雪花ID确保唯一性
	robotId := fmt.Sprintf("robot_%s", GenUid())
	robotName := "我是机器人"
	robotScore := 950 + int(math.Pow(float64(stepLv-30), 1.5))

	log.Debug("开始保存机器人 %s (等级:%d, 分数:%d)", robotId, stepLv, robotScore)

	// 添加到统一的集合中 方便维护
	err := db.GetRedis().SAdd(context.Background(), db.RKRobotMember(sid), robotId).Err()
	if err != nil {
		log.Error("saveRobot - SAdd error: %s, robotId: %s", err.Error(), robotId)
		return false
	}

	// 单独设置机器人基础数据
	err = db.GetRedis().HSet(context.TODO(), db.RKPlayerBaseInfo(robotId),
		"nickName", robotName,
		"avatarUrl", "").Err()
	if err != nil {
		log.Error("saveRobot - HSet BaseInfo error: %s, robotId: %s", err.Error(), robotId)
		// 清理已添加的集合数据
		db.GetRedis().SRem(context.Background(), db.RKRobotMember(sid), robotId)
		return false
	}
	// 单独设置机器人竞技场分数
	db.GetRedis().HSet(context.TODO(), db.RKPvpNormalDataOf(robotId), "score", robotScore)

	// 单独设置机器人竞技场数据
	serializer := ut.NewRedisHashSerializer(db.GetRedis())
	err = serializer.HSetField(context.TODO(), db.RKPvpNormalDataOf(robotId), "roles", roles)
	if err != nil {
		log.Error("saveRobot - HSetField error: %s, robotId: %s", err.Error(), robotId)
		// 清理已添加的数据
		db.GetRedis().SRem(context.Background(), db.RKRobotMember(sid), robotId)
		db.GetRedis().Del(context.TODO(), db.RKPlayerBaseInfo(robotId))
		return false
	}

	finalScore := float64(robotScore) + ut.TimeScore()
	err = db.GetRedis().ZAdd(context.TODO(), db.RKPvpNormalRank(sid), redis.Z{
		Score:  finalScore,
		Member: robotId,
	}).Err()
	if err != nil {
		log.Error("saveRobot - ZAdd error: %s, robotId: %s", err.Error(), robotId)
		// 清理已添加的数据
		db.GetRedis().SRem(context.Background(), db.RKRobotMember(sid), robotId)
		db.GetRedis().Del(context.TODO(), db.RKPlayerBaseInfo(robotId))
		db.GetRedis().Del(context.TODO(), db.RKPvpNormalDataOf(robotId))
		return false
	}

	log.Debug("成功保存机器人 %s", robotId)
	return true
}

func genEquip(passengers []*Passenger, stepLv int, r *cfg.RobotEquip[int]) {
	// 打造配置
	makeCfg := r.Equip
	// 熟练度
	proficiencyMap := make(map[string]int)

	// 根据配置 进行各个部位装备打造
	for j, v := range makeCfg {
		// 部位需要从1开始
		pos := j + 1
		// 桌子id需要从1开始
		tableId := v.Quality + 1
		key := fmt.Sprintf("%d-%d", tableId, pos)
		// 每个部位循环为每个乘客打造，直到消耗完次数
		passengerIndex := 0
		makeCnt := v.Cnt
		// 每个乘客的装备数据 只保留等级最高的
		passengerEquipMap := make(map[int]*EquipItem)
		for {
			if makeCnt <= 0 {
				break
			}
			curPassenger := passengers[passengerIndex]
			// 1 获取熟练度
			pro := proficiencyMap[key]
			// 2 计算打造台等级
			pLv := cfg.GetProficiencyLv(tableId, pro)
			makeBean, _ := cfg.EquipMakeContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", tableId, pLv))
			// 3 乘客要打造的装备id
			equipBean, _ := lo.Find(cfg.EquipContainer.GetData(), func(item *cfg.Equip[int]) bool {
				return item.Index == pos && item.RoleId == curPassenger.Id
			})
			// 打造装备
			equip := equipMake(equipBean.Id, makeBean.Level, pro)
			// 增加桌子熟练度
			proficiencyMap[key] = pro + 1
			equipTmp := passengerEquipMap[curPassenger.Id]
			if equipTmp == nil {
				passengerEquipMap[curPassenger.Id] = equip
			} else if equipTmp.Level < equip.Level {
				passengerEquipMap[curPassenger.Id] = equip
			}
			passengerIndex++
			if passengerIndex >= 5 {
				passengerIndex = 0
			}
			makeCnt--
		}
		// 打造完成后，为乘客应用该部位的装备
		for _, ps := range passengers {
			equip := passengerEquipMap[ps.Id]
			if equip != nil {
				equip.Used = true
				ps.Equips = append(ps.Equips, equip)
			}
		}
	}
}

func equipMake(id int, level int, pro int) *EquipItem {
	effects := cfg.GetEquipEffectsLv(id, level, pro)
	equip := &EquipItem{
		Uid:     GenUid(),
		Id:      id,
		Level:   level,
		Effects: make([]*EquipEffect, 0),
	}

	for _, effect := range effects {
		lv := ut.Random(effect.Min, effect.Max)
		if effect.Type == equip_effect_type.ATTR {
			lvs := make([]int, 0)
			attrType := enum.RoleAttack
			if effect.Target == equip_effect_target.HP {
				attrType = enum.RoleHP
			}
			for i := effect.Min; i <= effect.Max; i++ {
				if cfg.GetEquipLv(id, i, attrType) == i {
					lvs = append(lvs, i)
				}
			}
			idx := ut.Random(0, len(lvs)-1)
			lv = lvs[idx]
		}
		// 属性0 不予添加
		if lv == 0 {
			continue
		}
		attr := cfg.GetEquipEffectAttrByLv(id, effect, lv)
		equip.Effects = append(equip.Effects, &EquipEffect{Attr: attr, Id: effect.ID, Level: lv})
	}
	return equip
}

func genTalent(passengers []*Passenger, stepLv int, r *cfg.RobotEquip[int]) {
	for _, tmp := range passengers {
		talents := make([]*PassengerTalent, 0)
		for id, val := range r.Talent {
			talent := &PassengerTalent{Id: id + 1, Level: val}
			talents = append(talents, talent)
		}
		tmp.Talents = talents
	}
}

func monitorJackpot(x int) []*Passenger {
	result := make(map[int]*Passenger)
	fragMap := make(map[int]int)
	ary := cfg.CharacterContainer.GetData()
	for i := 0; i < x*x/30; i++ {
		// 随机乘客
		idx := ut.RandomIndexByWeight(ary, func(d *cfg.Character[int]) int { return d.Weight })
		r := ary[idx]
		_, ok := result[r.Id]
		if !ok {
			result[r.Id] = &Passenger{Id: r.Id}
			continue
		}
		// 转换成碎片
		fragMap[r.Id]++
	}

	// 碎片合成配置
	fragMergeCfg := cfg.Misc_CContainer.GetObj().FragUp
	for k, v := range fragMap {
		passenger := result[k]
		fragQualityMap := make(map[int]int)
		fragQualityMap[1] = v // 蓝
		fragQualityMap[2] = 0 // 紫
		fragQualityMap[3] = 0 // 橙
		fragQualityMap[4] = 0 // 红
		for {
			starBean, _ := cfg.StarUpContainer.GetBeanById(passenger.StarLv)
			if starBean == nil {
				break
			}
			fragNum := fragQualityMap[starBean.Quality]
			if fragNum >= starBean.UpCost {
				passenger.StarLv += 1
				fragNum -= starBean.UpCost
				fragQualityMap[starBean.Quality] = fragNum
				continue
			}
			// 低品质碎片
			fragNum = fragQualityMap[starBean.Quality-1]
			if fragNum == 0 {
				break
			}
			// 合成消耗
			cost := fragMergeCfg[starBean.Quality-1]
			if fragNum < cost {
				break
			}
			fragQualityMap[starBean.Quality] = fragNum / cost
			delete(fragQualityMap, starBean.Quality-1)
		}

	}
	return lo.Values(result)
}
