package structs

import (
	"fmt"
	"train/base/cfg"
	"train/base/enum/build_attr"
	"train/base/enum/train_tech_type"
	"train/common/pb"

	"github.com/samber/lo"
)

func NewTechModule() *TechModule {
	return &TechModule{Data: make(map[string]int)}
}

type TrainTech struct {
	Id           string                 `bson:"id"`
	Level        int                    `bson:"level"`
	json         *cfg.TrainTech[string] `bson:"-"`
	EffectValue  int                    `bson:"-"`
	EffectTarget int                    `bson:"-"`
	EffectType   int                    `bson:"-"`
}

func (t *TrainTech) Init(id string) {
	t.Id = id
	t.initEffect()
}

func (t *TrainTech) GetJson() *cfg.TrainTech[string] {
	if t.json == nil {
		t.json = cfg.TrainTechContainer.GetBean(t.Id)
	}
	return t.json
}

func (t *TrainTech) initEffect() {
	for i := 1; i <= t.Level; i++ {
		json := cfg.TrainTechLevelContainer.GetBean(fmt.Sprintf("%s-%d", t.Id, i))
		t.EffectValue += json.Effect.Num
		t.EffectTarget = json.Effect.Target
		t.EffectType = json.Effect.Type
	}
}

type TechModule struct {
	Data  map[string]int        `bson:"data"` // 科技数据
	plr   *Player               `bson:"-"`
	Techs map[string]*TrainTech `bson:"-"`
}

func (t *TechModule) init(plr *Player) {
	t.plr = plr
	if t.Data == nil {
		t.Data = make(map[string]int)
	}
	if t.Techs == nil {
		t.Techs = make(map[string]*TrainTech)
	}
	for k, v := range t.Data {
		tech := &TrainTech{Id: k, Level: v}
		tech.initEffect()
		t.Techs[k] = tech
	}
}

func (t *TechModule) ToPb() *pb.TechData {
	return &pb.TechData{
		Data: lo.MapEntries(t.Data, func(key string, val int) (string, int32) { return key, int32(val) }),
	}
}

func (t *TechModule) GetTechLevel(id string) int { return t.Data[id] }

func (t *TechModule) Upgrade(id string) {
	t.Data[id] = 1 + t.GetTechLevel(id)

	tech := &TrainTech{Id: id, Level: t.Data[id]}
	tech.initEffect()
	t.Techs[id] = tech
}

func (t *TechModule) GetCarriageRoleCnt(carriageId int) int {
	return t.GetEffectValue(train_tech_type.CARRIAGE_ROLE_CNT, carriageId)
}

func (t *TechModule) GetTrainSpeed() int {
	return t.GetEffectValue(train_tech_type.TRAIN_SPEED, 0)
}

func (t *TechModule) GetTrainDailyTask() int {
	return t.GetEffectValue(train_tech_type.TRAIN_DAILY_TASK, 0)
}

func (t *TechModule) GetDeepExplore() int {
	return t.GetEffectValue(train_tech_type.DEEP_EXPLORE, 0)
}

func (t *TechModule) GetTimeMachine() int {
	return t.GetEffectValue(train_tech_type.TIME_MACHINE, 0)
}

func (t *TechModule) GetTrainLoad() int {
	return t.GetEffectValue(train_tech_type.TRAIN_LOAD, 0)
}

func (t *TechModule) GetShip() int {
	return t.GetEffectValue(train_tech_type.SHIP, 0)
}

func (t *TechModule) GetCarriageAttr(carriageId int, attr string) int {
	switch attr {
	case build_attr.STAR:
		return t.GetEffectValue(train_tech_type.STAR_DUST, carriageId)
	case build_attr.HEART:
		return t.GetEffectValue(train_tech_type.HERT, carriageId)
	case build_attr.ELECTRICITY:
		return t.GetEffectValue(train_tech_type.ELECTRIC, carriageId)
	case build_attr.WATER:
		return t.GetEffectValue(train_tech_type.WATER, carriageId)
	case build_attr.VITALITY:
		return t.GetEffectValue(train_tech_type.VITALITY, carriageId)
	}
	return 0
}

func (t *TechModule) GetCarriageAttrPercent(carriageId int, attr string) float64 {
	ret := 0
	switch attr {
	case build_attr.STAR:
		ret = t.GetEffectValue(train_tech_type.STAR_DUST_PERCENT, carriageId)
	case build_attr.HEART:
		ret = t.GetEffectValue(train_tech_type.HERT_PERCENT, carriageId)
	case build_attr.ELECTRICITY:
		ret = t.GetEffectValue(train_tech_type.ELECTRIC_PERCENT, carriageId)
	case build_attr.WATER:
		ret = t.GetEffectValue(train_tech_type.WATER_PERCENT, carriageId)
	case build_attr.VITALITY:
		ret = t.GetEffectValue(train_tech_type.VITALITY_PERCENT, carriageId)
	}
	return float64(ret) / 100.0
}

func (t *TechModule) GetEffectValue(_type int, target int) int {
	for _, tech := range t.Techs {
		if tech.EffectType == _type && tech.EffectTarget == target {
			return tech.EffectValue
		}
	}
	return 0
}
