package structs

import (
	"fmt"
	"strings"
	"train/base/cfg"
	"train/base/enum"
	"train/base/enum/function_type"
	"train/base/event"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/sasha-s/go-deadlock"

	"github.com/huyangv/vmqant/log"
	"github.com/spf13/cast"
)

type PlanetData struct {
	deadlock.RWMutex   `bson:"-"`
	Planets            map[int]*Planet `bson:"planets"`            // 玩家已经解锁的星球当前进度数据
	ToSailingStartTime int             `bson:"ToSailingStartTime"` // 航行开始时间
	ToSailingAddTime   int             `bson:"ToSailingAddTime"`   // 航行增加时间
	TargetPlanetId     int             `bson:"targetPlanetId"`     // 航行目标星球id
	CurPlanetId        int             `bson:"curPlanetId"`        // 当前正在哪个星球探索
	MaxReachNodeId     string          `bson:"maxReachNodeId"`     // 最大探索到的节点
	plr                *Player         `bson:"-"`
	// RageMode           *RageMode       `bson:"rageMode"` // 狂暴模式
}

func NewDefaultPlanetData() *PlanetData {
	data := &PlanetData{
		Planets: make(map[int]*Planet),
	}
	data.CurPlanetId = enum.DefaultPlanet
	return data
}

func (this *PlanetData) Init(plr *Player) {
	for _, planet := range this.Planets {
		planet.Init(plr)
	}
	this.plr = plr
	this.UnlockPlanetByDefault()
}

func (this *PlanetData) DailyRefresh() {
}

// ToPb() 转pb数据
func (this *PlanetData) ToPb() *pb.PlanetInfo {
	return &pb.PlanetInfo{
		CurPlanetId:     cast.ToInt32(this.CurPlanetId),
		MoveSurplusTime: cast.ToInt32(this.GetMoveSurplusTime()),
		Planets:         this.PlanetsToPbArray(),
		MoveTargetId:    cast.ToInt32(this.TargetPlanetId),
	}
}

// PlanetsToPbArray 星球数组单独转pb数组
func (this *PlanetData) PlanetsToPbArray() (arr []*pb.Planet) {
	for _, planet := range this.Planets {
		arr = append(arr, planet.ToPb())
	}
	return
}

func (this *PlanetData) addToSailingAddTime(addVal int) {
	this.ToSailingAddTime += addVal

	// 检查稀有运送战斗卡点
	transportMod := this.plr.Transport
	data := transportMod.GetBattleData(this.TargetPlanetId)
	if data != nil && data.End == this.TargetPlanetId && data.BattleData != nil {
		curPlanetId := this.CurPlanetId
		bean, _ := cfg.PlanetMoveTimeContainer.GetBeanById(curPlanetId)
		costTime := bean.Time[this.TargetPlanetId] * ut.TIME_SECOND
		transportBattle := data.BattleData
		stopTimePoint := costTime - transportBattle.Second*ut.TIME_SECOND
		this.ToSailingAddTime = ut.Min(this.ToSailingAddTime, costTime-stopTimePoint)
	}
}

// IsMove 是否正在航行
func (this *PlanetData) IsMove() bool {
	return this.TargetPlanetId > 0
}

func (this *PlanetData) CancelMove() {
	log.Info("[%s] CancelMove:%d", this.plr.GetUid(), this.TargetPlanetId)
	this.ReachPlanet(this.CurPlanetId)
}

// 航行到某个星球
func (plr *Player) MoveToPlanet(id int) {
	Planet := plr.PlanetData

	Planet.TargetPlanetId = id

	Planet.ToSailingStartTime = plr.GetNowTime()
	plr.eventCenter.Emit(event.TrainMovingPlanet, id)
	log.Info("[%s] 玩家开始前往星球:%d", plr.GetUid(), id)
}

// UpdateMoveToPlanet 更新航行状态
func (plr *Player) UpdateMoveToPlanet() {
	Planet := plr.PlanetData
	if !Planet.IsMove() {
		return
	}
	time := Planet.GetMoveSurplusTime()
	if time > 0 {
		return
	}
	plr.PlanetData.ReachPlanet(Planet.TargetPlanetId)

	transportMod := plr.Transport
	if transportMod != nil {
		t := transportMod.GetBattleData(Planet.TargetPlanetId)
		if t != nil {
			t.CheckAllPass()
		}
	}
}

// GetMoveSurplusTime 获取航行剩余时间
func (this *PlanetData) GetMoveSurplusTime() int {
	if !this.IsMove() {
		return 0
	}

	curPlanetId := this.CurPlanetId
	bean, _ := cfg.PlanetMoveTimeContainer.GetBeanById(curPlanetId)
	costTime := bean.Time[this.TargetPlanetId] * ut.TIME_SECOND

	transportMod := this.plr.Transport

	if transportMod != nil {
		data, _ := lo.Find(transportMod.List, func(item *TransportData) bool {
			return item.State == pb.TransportDataState_Pull
		})
		if data != nil {
			costTime *= cfg.Misc_CContainer.GetObj().Transport.TimeRate
		}
		// 仅稀有任务才会有战斗
		data = transportMod.GetBattleData(this.TargetPlanetId)
		// 航行只卡稀有任务的终点
		if data != nil && data.End == this.TargetPlanetId {
			transportBattle := data.BattleData
			if transportBattle != nil {
				stopTimePoint := costTime - transportBattle.Second*ut.TIME_SECOND
				if this.plr.GetNowTime()-this.ToSailingStartTime-this.ToSailingAddTime > stopTimePoint {
					return costTime - stopTimePoint
				}
			}
		}
	}

	return ut.Max(0, (this.ToSailingStartTime+costTime)-this.ToSailingAddTime-this.plr.GetNowTime())
}

// ReachPlanet 到达星球
func (Planet *PlanetData) ReachPlanet(id int) {
	// 登陆后改变当前星球id
	Planet.CurPlanetId = id
	Planet.GetCurPlanet().Reached = true
	// 清理航行数据
	//Planet.ToSailingEndTime = 0
	Planet.ToSailingStartTime = 0
	Planet.ToSailingAddTime = 0
	Planet.TargetPlanetId = 0
	log.Info("[%s] 玩家到达星球:%d", Planet.plr.GetUid(), Planet.CurPlanetId)
}

func (plr *Player) LandPlanet() {
	Planet := plr.PlanetData
	planet := Planet.GetCurPlanet()
	planet.Landed = true
	log.Info("[%s] 玩家登录星球:%d", plr.GetUid(), Planet.CurPlanetId)
}

// 根据lockId解锁星球
func (this *PlanetData) UnlockPlanetByPre(id any) {
	datas := cfg.PlanetContainer.GetData()
	for _, data := range datas {
		if cast.ToString(data.Lock["id"]) == cast.ToString(id) {
			this.UnlockPlanet(data.Id)
		}
	}
}

func (this *PlanetData) UnlockPlanetByDefault() {
	datas := cfg.PlanetContainer.GetData()
	for _, data := range datas {
		if data.Lock == nil {
			this.UnlockPlanet(data.Id)
		}
	}
}

func (this *PlanetData) UnlockPlanet(id int) *Planet {
	planet := this.GetPlanet(id)
	if planet != nil {
		return planet
	}
	bean, _ := cfg.PlanetContainer.GetBeanById(id)
	if bean == nil {
		return nil
	}
	// 该星球的地图是否准备好
	_, exists := cfg.PlanetMapContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", id, bean.GetDefaultMapId()))
	if !exists {
		return nil
	}
	planet = NewPlanetFromCfg(*bean)
	planet.Init(this.plr)
	planet.UnlockTime = this.plr.GetNowTime()
	this.Planets[id] = planet
	if this.plr != nil {
		this.plr.eventCenter.Emit(event.PlanetUnlock, planet.Id)
	}
	return planet
}

// GetCurPlanet 获取当前所处星球
func (this *PlanetData) GetCurPlanet() (plant *Planet) {
	return this.GetPlanet(this.CurPlanetId)
}

func (this *PlanetData) GetPlanet(id int) *Planet {
	return this.Planets[id]
}

// 是否已完成此节点
func (this *PlanetData) isPassNode(id string) bool {
	strs := strings.Split(id, "-")
	planetId := cast.ToInt(strs[0])
	mapId := cast.ToInt(strs[1])
	nodeId := cast.ToInt(strs[2])
	planet := this.GetPlanet(planetId)
	if planet == nil {
		return false
	}
	if planet.IsDone() {
		return true
	}
	if planet.MapId == mapId {
		return planet.CurNodeId > nodeId
	}
	return planet.MapId >= mapId
}

func (this *PlanetData) isPassBattle(id string) bool {
	strs := strings.Split(id, "-")
	planetId := cast.ToInt(strs[0])
	mapId := cast.ToInt(strs[1])
	planet := this.GetPlanet(planetId)
	if planet == nil {
		return false
	}
	if planet.IsDone() {
		return true
	}
	if planet.MapId > mapId {
		return true
	}
	if planet.MapId < mapId {
		return false
	}
	cfg := planet.getCfgCheckPoint(mapId, id)
	if cfg == nil {
		return false
	}
	return planet.CurNodeId > cfg.Index
}

func (this *PlanetData) GetLastPlanet() *Planet {
	datas := cfg.PlanetContainer.GetData()
	for i := len(datas) - 1; i >= 0; i-- {
		data := datas[i]
		planet := this.GetPlanet(data.Id)
		if planet != nil {
			return planet
		}
	}
	return nil
}

// GetLastBattleNode 获取最后一个通关的战斗节点  boss:是否区分boss/普通怪
func (this *PlanetData) GetLastBattleNode(boss bool) *cfg.MapBase {
	datas := cfg.PlanetContainer.GetData()
	for i := len(datas) - 1; i >= 0; i-- {
		data := datas[i]
		planet := this.GetPlanet(data.Id)
		if planet != nil {
			node := planet.GetLastBattleNode(boss)
			if node != nil {
				return node
			}
		}
	}
	//只有不走新手的时候会走这里
	curPlanet := this.GetCurPlanet()
	bean, _ := cfg.PlanetContainer.GetBeanById(this.CurPlanetId)
	mapData := bean.GetMapData(curPlanet.MapId)
	node := array.Find(mapData, func(n *cfg.MapBase) bool { return n.Type == enum.CHECKPOINT })
	return node
}

// Planet 星球数据
type Planet struct {
	Id                       int              `bson:"id"`                         // 星球id
	MapId                    int              `bson:"mapId"`                      // 当前地图
	CurNodeId                int              `bson:"curNodeId"`                  // 当前节点id，是所在地图下的id，从1开始
	NodeProgress             float64          `bson:"nodeProgress,omitempty"`     //当前节点的进度
	NodePathProgress         int              `bson:"nodePathProgress,omitempty"` //从上一个节点到当前节点的移动进度
	NodeData                 map[string]any   `bson:"nodeData,omitempty"`         // 当前节点数据
	Landed                   bool             `bson:"landed"`                     //是否登陆过星球
	Reached                  bool             `bson:"reached"`                    //是否到达过星球
	maps                     [][]*cfg.MapBase `bson:"-"`                          //地图数据
	Branches                 []*PlanetBranch  `bson:"branches"`                   //支线
	UnlockTime               int              `bson:"unlockTime"`
	DoneTime                 int              `bson:"doneTime"`
	json                     *cfg.Planet[int] `bson:"-"`
	plr                      *Player          `bson:"-"`
	ProfileData              map[int]int      `bson:"profileData,omitempty"`              // 星球资料
	ProfileCollectReward     []int            `bson:"profileCollectReward,omitempty"`     // 星球资料收集奖励领取情况 配置的group组
	RoleNum                  int              `bson:"roleNum,omitempty"`                  // 宣传玩法人口数
	PublicityUnGetOutputTime int              `bson:"publicityUnGetOutputTime,omitempty"` // 宣传玩法未收取时长累计 用来给客户端表现百分比的
	PublicityOutputTime      int              `bson:"publicityOutputTime,omitempty"`      // 宣传玩法产出时长 用来计算奖励的
	PublicityOutput          *CarriageOutPut  `bson:"publicityOutput,omitempty"`          // 宣传玩法产出
}

func (this *Planet) Init(plr *Player) {
	this.plr = plr
	config, _ := cfg.PlanetContainer.GetBeanById(this.Id)
	mapId := config.GetDefaultMapId()
	maps := make([][]*cfg.MapBase, 0)
	if this.NodeData == nil {
		this.NodeData = make(map[string]any)
	}
	for {
		data := config.GetMapData(mapId)
		if len(data) <= 0 {
			break
		}
		maps = append(maps, data)
		mapId++
	}
	this.maps = maps

	if this.Landed || this.Id == enum.DefaultPlanet {
		this.Reached = true
	}

	this.Branches = lo.Filter(this.Branches, func(item *PlanetBranch, index int) bool {
		return item.GetJson() != nil
	})
	if len(this.Branches) <= 0 {
		datas := lo.Filter(cfg.PlanetBranchContainer.GetData(), func(item *cfg.PlanetBranch[string], index int) bool {
			return item.PlanetId == this.Id
		})
		this.Branches = lo.Map(datas, func(item *cfg.PlanetBranch[string], index int) *PlanetBranch {
			return &PlanetBranch{Id: item.Id}
		})
	}
	for _, b := range this.Branches {
		b.init()
	}
	if this.ProfileData == nil {
		this.ProfileData = make(map[int]int)
	}
	if this.PublicityOutput == nil {
		this.PublicityOutput = &CarriageOutPut{}
	}
}

func (this *Planet) GetJson() *cfg.Planet[int] {
	if this.json == nil {
		this.json, _ = cfg.PlanetContainer.GetBeanById(this.Id)
	}
	return this.json
}

func (this *Planet) ToPb() *pb.Planet {
	return &pb.Planet{
		Id:               cast.ToInt32(this.Id),
		CurMapId:         cast.ToInt32(this.MapId),
		CurNodeId:        cast.ToInt32(this.CurNodeId),
		NodeProgress:     this.NodeProgress,
		NodePathProgress: cast.ToInt32(this.NodePathProgress),
		Landed:           this.Landed,
		Reached:          this.Reached,
		Branches: lo.Map(this.Branches, func(item *PlanetBranch, index int) *pb.PlanetBranch {
			return item.ToPb()
		}),
		ProfileData:              lo.MapEntries(this.ProfileData, func(key int, val int) (int32, int32) { return int32(key), int32(val) }),
		ProfileCollectReward:     ut.ToInt32(this.ProfileCollectReward),
		RoleNum:                  cast.ToInt32(this.RoleNum),
		PublicityUnGetOutputTime: int32(this.PublicityUnGetOutputTime),
		PublicityOutputTime:      int32(this.PublicityOutputTime),
	}
}

func (this *Planet) GetPublicityReward() *Condition {
	info := this.GetPublicityInfo()
	if info == nil {
		return nil
	}
	cond := ConfigConditionConvert(info.Reward).One()
	if cond == nil {
		return nil
	}
	outputObj := this.GetPublicityOutput()
	if outputObj == nil {
		return nil
	}
	num := cast.ToInt(outputObj.Val)
	if num <= 0 {
		return nil
	}
	outputObj.Val -= cast.ToFloat64(num)
	outputObj.AccTotal += num
	cond.Num = num
	this.plr.GrantReward(cond, ta.ResChangeSceneTypeNoReport)
	this.PublicityUnGetOutputTime = 0
	return cond
}

func (this *Planet) GetPublicityOutput() *CarriageOutPut {
	if this == nil || !this.IsDone() || this.RoleNum == 0 {
		return nil
	}
	return this.PublicityOutput
}

// 一管能量 = 30分钟收益
func (this *Planet) UpdatePublicityOutputByPassTime(passTime int) {
	if this == nil || !this.plr.IsUnlockFunction(function_type.PLANET_PUBLICITY) {
		return
	}
	if this.RoleNum == 0 {
		return
	}
	info := this.GetPublicityInfo()
	if info == nil {
		return
	}

	this.PublicityUnGetOutputTime += passTime
	this.PublicityOutputTime += passTime
	rate := 1.0
	time := ut.TIME_HOUR
	count := ut.Floor(float64(this.PublicityOutputTime) / float64(time))
	if count <= 0 {
		return
	}

	if info.Rate <= 0 {
		return
	}
	output := ut.Float64(info.Rate*count) * rate
	this.PublicityOutput.Val += output
	this.PublicityOutputTime -= count * time
}

func (this *Planet) SetCur(config *cfg.MapBase) int {
	this.CurNodeId = config.Index
	this.NodeProgress = 0
	this.NodePathProgress = 0
	this.NodeData = make(map[string]any)
	return this.CurNodeId
}

func (this *Planet) GetMapDataById(id int) (data *cfg.MapBase) {
	bean := this.GetJson()
	mapData := bean.GetMapData(this.MapId)
	if len(mapData) <= 0 {
		return
	}
	if id < 1 {
		return
	}
	data = mapData[id-1]
	return
}

func (this *Planet) PassCurNode() {
	bean := this.GetJson()
	mapData := bean.GetMapData(this.MapId)
	nextId := this.CurNodeId + 1

	// 当前地图节点上限，该切换地图了
	if len(mapData) < nextId {
		mapData := bean.GetMapData(this.MapId + 1)
		if len(mapData) <= 0 {
			// 没有地图了 当前星球探索完毕
			this.MapId = enum.PlanetExploreFinishedMapId
			return
		}
		this.MapId += 1
		defaultNode := bean.GetDefaultNode(this.MapId)
		this.SetCur(defaultNode)
	} else {
		// 下一个节点
		data := this.GetMapDataById(nextId)
		this.SetCur(data)
	}
}

func (this *Planet) IsDone() bool {
	return this.MapId == enum.PlanetExploreFinishedMapId
}

// GetLastBattleNode 获取最后一个通关的战斗节点   boss:是否区分boss/普通怪
func (this *Planet) GetLastBattleNode(boss bool) *cfg.MapBase {
	mapId := this.MapId
	maps := this.maps
	nodeId := this.CurNodeId - 1
	if mapId == 1 && this.CurNodeId == 1 {
		return nil
	}
	if this.IsDone() {
		mapId = len(this.maps)
		nodeId = 0
	}
	for {
		mapData := maps[mapId-1]
		if nodeId <= 0 {
			nodeId = len(mapData)
		}
		for {
			data := mapData[nodeId-1]
			if data.Type == enum.CHECKPOINT {
				chap, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(data.TypeId)
				if boss && !chap.IsBoss() {
					return data
				}
				return data
			}
			nodeId--
			if nodeId <= 0 {
				break
			}
		}
		mapId--
		if mapId <= 0 {
			return nil
		}
	}
}

func (this *Planet) getPercent() int {
	if this.IsDone() {
		return 100
	}
	return 0
}

func (this *Planet) getMapPercent(id int) int {
	if this.IsDone() || id < this.MapId {
		return 100
	}
	if id > this.MapId {
		return 0
	}
	mapData := this.maps[id-1]
	cur := this.CurNodeId - 1
	max := len(mapData)
	return int(float64(cur) / float64(max) * 100)
}

func (this *Planet) getCfgCheckPoint(mapId int, typeId string) *cfg.MapBase {
	mapData := this.maps[mapId-1]
	if mapData != nil {
		for _, v := range mapData {
			if v.Type == enum.CHECKPOINT && v.TypeId == typeId {
				return v
			}
		}
	}
	return nil
}

func (this *Planet) GetBranch(branchId string) *PlanetBranch {
	return array.Find(this.Branches, func(b *PlanetBranch) bool { return b.Id == branchId })
}

func (this *Planet) IsProfileCollectRewardGet(area, step int) bool {
	for i := 0; i < len(this.ProfileCollectReward); {
		areaT := this.ProfileCollectReward[i]
		i++
		stepT := this.ProfileCollectReward[i]
		i++
		if areaT != area {
			continue
		}
		if stepT == step {
			return true
		}
	}
	return false
}

func (this *Planet) GetPublicityInfo() *cfg.PublicityPlay[string] {
	ary := lo.Filter(cfg.PublicityPlayContainer.GetData(), func(item *cfg.PublicityPlay[string], index int) bool {
		return item.PlanetId == this.Id
	})
	var info *cfg.PublicityPlay[string]
	for i := 0; i < len(ary); i++ {
		if this.RoleNum >= ary[i].Req {
			info = ary[i]
		}
	}
	return info
}
func (this *Planet) GetPublicityNextInfo() *cfg.PublicityPlay[string] {
	ary := lo.Filter(cfg.PublicityPlayContainer.GetData(), func(item *cfg.PublicityPlay[string], index int) bool {
		return item.PlanetId == this.Id
	})
	cur := this.GetPublicityInfo()
	if cur == nil {
		return ary[0]
	}
	_, idx, _ := lo.FindIndexOf(ary, func(item *cfg.PublicityPlay[string]) bool { return item == cur })
	if idx == -1 || idx == len(ary)-1 {
		return nil
	}
	return ary[idx+1]
}

func (this *PlanetData) GetPlanetNodeRewards(data *cfg.MapBase) []*Condition {
	rewards := make([]*cfg.ChestReward, 0)
	_type := data.Type
	id := data.TypeId
	if data.IsBranch {
		switch _type {
		case enum.NONE:
			bean, _ := cfg.BranchPlanetSpContainer.GetBeanByUnique(id)
			if bean != nil {
				rewards = bean.Rewards
			}
		case enum.MINE:
			bean, _ := cfg.BranchPlanetMineContainer.GetBeanByUnique(id)
			rewards = bean.Rewards
		case enum.CHECKPOINT:
			bean, _ := cfg.BranchPlanetMonsterContainer.GetBeanByUnique(id)
			rewards = bean.Rewards
		}
	} else {
		switch _type {
		case enum.NONE:
			bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(id)
			if bean != nil {
				rewards = bean.Rewards
			}
		case enum.MINE:
			bean, _ := cfg.ChapterPlanetMineContainer.GetBeanByUnique(id)
			if bean != nil {
				rewards = bean.Rewards
			} else {
				log.Error("ChapterPlanetMineContainer not found %s", id)
			}
		case enum.CHECKPOINT:
			bean, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(id)
			if bean != nil {
				rewards = bean.Rewards
			} else {
				log.Error("ChapterPlanetMonsterContainer not found %s", id)
			}
		}
	}
	return this.plr.GenerateRewards(rewards, nil)
}

func NewPlanetFromCfg(config cfg.Planet[int]) *Planet {
	// 初始节点
	mapId := config.GetDefaultMapId()
	this := &Planet{
		Id:    config.GetUnique(),
		MapId: mapId,
	}
	defaultNode := config.GetDefaultNode(mapId)
	if defaultNode != nil {
		this.SetCur(defaultNode)
	} else {
		this.MapId = enum.PlanetExploreFinishedMapId
	}
	return this
}

func (plr *Player) GetMaxMainLevelId() string {
	node := plr.PlanetData.MaxReachNodeId
	if node == "" {
		return "0-0-0"
	}
	return node
}

// PassPlanetCurNode 通过当前星球的当前节点
//
// Parameters:
//   - planet *Planet
func (plr *Player) PassPlanetCurNode(planet *Planet) {
	nodeUid := fmt.Sprintf("%d-%d-%d", planet.Id, planet.MapId, planet.CurNodeId)
	plr.PlanetData.MaxReachNodeId = nodeUid
	log.Info("[%s] 通过主线节点 %s", plr.GetUid(), nodeUid)

	plr.RecordPassNode(nodeUid)

	data := planet.GetMapDataById(planet.CurNodeId)

	planet.PassCurNode()

	if data.Type == enum.MINE {
		if plr.Tool.BlessCount > 0 {
			plr.Tool.ChangeBlessCount(-1)
		}
	}
	plr.eventCenter.Emit(event.PlanetNodeComplete, planet.Id, nodeUid)

	if planet.IsDone() {
		plr.PlanetData.UnlockPlanetByPre(planet.Id)
		planet.DoneTime = plr.GetNowTime()
		plr.eventCenter.Emit(event.PlanetComplete, planet.Id)
	} else if data.Type == enum.CHECKPOINT {
		plr.PlanetData.UnlockPlanetByPre(data.TypeId)
	}
}

func (plr *Player) PassBranchPlanetNode(branch *PlanetBranch) {
	nodeUid := fmt.Sprintf("%d-%d-%d", branch.GetJson().PlanetId, branch.MapId, branch.NodeId)
	log.Info("[%s] 通过支线节点 %s", plr.GetUid(), nodeUid)
	// preData := planet.GetMapDataById(planet.CurNodeId)
	branch.PassNode()
	// plr.eventCenter.Emit(event.PlanetNodeComplete, planet.Id, nodeUid)
}

func (plr *Player) GetReachedPlanets() []*Planet {
	reacheds := make([]*Planet, 0)
	if plr.PlanetData == nil || plr.PlanetData.Planets == nil {
		return reacheds
	}
	for _, planet := range plr.PlanetData.Planets {
		if planet.Reached {
			reacheds = append(reacheds, planet)
		}
	}
	return reacheds
}

// GetRandomReachedPlanet
/*
 * @description 随机获取一个已经到达的星球 可能返回nil
 * @param excludePlanetIds 需要排除的星球id
 * @return *Planet
 */
func (plr *Player) GetRandomReachedPlanet(excludePlanetIds []int) *Planet {
	reachedPlanets := plr.GetReachedPlanets()
	reachedPlanets = lo.Filter(reachedPlanets, func(planet *Planet, i int) bool {
		_, exists := lo.Find(excludePlanetIds, func(id int) bool {
			return id == planet.Id
		})
		if exists {
			return false
		}
		return true
	})
	if len(reachedPlanets) == 0 {
		return nil
	}
	idx := ut.Random(0, len(reachedPlanets)-1)
	return reachedPlanets[idx]
}

type PlanetBranch struct {
	Id          string                               `bson:"id"`          //支线id
	MapId       int                                  `bson:"mapId"`       // 当前星球地图id
	NodeId      int                                  `bson:"curNodeId"`   // 当前节点id，是所在地图下的id，从1开始
	NodeRewards []int                                `bson:"nodeRewards"` // 已领取的节点奖励
	json        *cfg.PlanetBranch[string]            `bson:"-" json:"-"`
	maps        map[int]*cfg.BranchPlanetMap[string] `bson:"-" json:"-"`
}

func (b *PlanetBranch) ToPb() *pb.PlanetBranch {
	return &pb.PlanetBranch{
		Id:          b.Id,
		MapId:       int32(b.MapId),
		NodeId:      int32(b.NodeId),
		NodeRewards: ut.ToInt32(b.NodeRewards),
	}
}

func (b *PlanetBranch) GetJson() *cfg.PlanetBranch[string] {
	if b.json == nil {
		b.json, _ = cfg.PlanetBranchContainer.GetBeanByUnique(b.Id)
	}
	return b.json
}

func (b *PlanetBranch) init() {
	json := b.GetJson()
	b.maps = make(map[int]*cfg.BranchPlanetMap[string])
	for _, mapId := range json.Maps {
		b.maps[mapId], _ = cfg.BranchPlanetMapContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", b.GetJson().PlanetId, mapId))
	}
	if b.NodeId == 0 {
		b.NodeId = 1
	}
	if b.MapId == 0 {
		b.MapId = 1
	}
}

func (b *PlanetBranch) GetNodeData(mapId int, nodeId int) *cfg.MapBase {
	Map := b.GetMapData(mapId)
	return Map.Node[nodeId-1]
}

func (b *PlanetBranch) GetMapData(mapId int) *cfg.BranchPlanetMap[string] {
	return b.maps[mapId]
}

func (b *PlanetBranch) PassNode() {
	mapData := b.GetMapData(b.MapId)
	nextId := b.NodeId + 1
	b.NodeRewards = make([]int, 0)

	// 当前地图节点上限，该切换地图了
	if len(mapData.Node) < nextId {
		mapData := b.GetMapData(b.MapId + 1)
		if mapData == nil || len(mapData.Node) <= 0 {
			// 没有地图了 当前星球探索完毕
			b.MapId = enum.PlanetExploreFinishedMapId
			return
		}
		b.MapId += 1
	} else {
		// 下一个节点
		b.NodeId = nextId
	}
}

func (b *PlanetBranch) IsDone() bool {
	return b.MapId == enum.PlanetExploreFinishedMapId
}

type RageMode struct {
	Id      string `bson:"id"`      // 狂暴模式节点id
	Count   int    `bson:"count"`   // 狂暴模式次数
	EndTime int    `bson:"endTime"` // 狂暴模式结束时间
}

func (this *RageMode) toPb() *pb.RageMode {
	return &pb.RageMode{
		Id:    this.Id,
		Count: cast.ToInt32(this.Count),
	}
}
