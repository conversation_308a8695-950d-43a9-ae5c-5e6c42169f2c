package structs

import (
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/enum/daily_task_state"
	"train/base/enum/daily_task_type"
	"train/base/enum/function_type"
	"train/common/pb"
	ut "train/utils"
	"train/utils/array"

	"github.com/samber/lo"
	"github.com/spf13/cast"
)

type DailyTaskModule struct {
	Tasks  []*DailyTask `bson:"tasks"`  // 任务数据
	BigGet bool         `bson:"bigGet"` // 是否领取大奖励
	plr    *Player      `bson:"-"`
}

func NewDailyTaskModule() *DailyTaskModule {
	module := &DailyTaskModule{}
	return module
}

func (n *DailyTaskModule) ToPb() *pb.DailyTaskInfo {
	return &pb.DailyTaskInfo{
		BigGet: n.BigGet,
		Tasks:  lo.Map(n.Tasks, func(item *DailyTask, index int) *pb.DailyTask { return item.ToPb() }),
	}
}

type DailyTask struct {
	Uid        string              `bson:"uid"`
	Id         int                 `bson:"id"`
	Target     []*Condition        `bson:"target"`
	Progress   []*Condition        `bson:"progress"` //进度
	Reward     []*Condition        `bson:"reward"`
	State      int                 `bson:"state"`
	Sender     int                 `bson:"sender"` //发起者
	Content    int                 `bson:"content"`
	Planet     int                 `bson:"planet"`     // 战斗所在星球
	BattleInfo []*BattleRole       `bson:"battleInfo"` // 战斗数据
	json       *cfg.DailyTask[int] `bson:"-"`
}

func (d *DailyTask) GetJson() *cfg.DailyTask[int] {
	if d.json == nil {
		d.json, _ = cfg.DailyTaskContainer.GetBeanById(d.Id)
	}
	return d.json
}

func (d *DailyTask) Finish() { d.State = daily_task_state.FINISH }

func (d *DailyTask) CheckProgress() bool {
	if d.GetJson().Type != daily_task_type.DIALOG {
		return true
	}
	return lo.EveryBy(d.Target, func(item *Condition) bool {
		progress := array.Find(d.Progress, func(p *Condition) bool {
			return p.IsSame(item)
		})
		if progress == nil {
			return false
		}
		return progress.Num >= item.GetNum()
	})
}

func (d *DailyTask) AddProgress(targets []*Condition) {
	for _, target := range targets {
		progress := array.Find(d.Progress, func(p *Condition) bool {
			return p.IsSame(target)
		})
		if progress == nil {
			d.Progress = append(d.Progress, target)
		} else {
			progress.Num += target.GetNum()
		}
	}
}

func (d *DailyTask) ToPb() *pb.DailyTask {
	if d == nil {
		return nil
	}
	return &pb.DailyTask{
		Uid: d.Uid,
		Id:  cast.ToInt32(d.Id),
		Target: lo.Map(d.Target, func(item *Condition, index int) *pb.Condition {
			return item.ToPb()
		}),
		Reward: lo.Map(d.Reward, func(item *Condition, index int) *pb.Condition {
			return item.ToPb()
		}),
		Progress: lo.Map(d.Progress, func(item *Condition, index int) *pb.Condition {
			return item.ToPb()
		}),
		State:      cast.ToInt32(d.State),
		Sender:     cast.ToInt32(d.Sender),
		Content:    int32(d.Content),
		Planet:     int32(d.Planet),
		BattleInfo: lo.Map(d.BattleInfo, func(item *BattleRole, index int) *pb.BattleRole { return item.ToPb() }),
	}
}

func (c *DailyTaskModule) init(plr *Player) { c.plr = plr }

func (c *DailyTaskModule) CheckAndRefresh() {
	if !c.plr.IsUnlockFunction(function_type.DAILY_TASK) {
		return
	}
	c.Refresh()
}

func (c *DailyTaskModule) RandomDailyTask(first bool) []*DailyTask {
	ary := make([]*DailyTask, 0)
	taskBeans := cfg.DailyTaskContainer.GetData()
	// 获取所有乘客
	allPassengers := c.plr.Passenger

	collectTargetMap := make(map[int]bool)
	datas := cfg.DailyTaskItemContainer.GetData()
	maxCount := 5

	totalCount := cfg.Misc_CContainer.GetObj().DailyTask.Count

	i := 0
	for ; i < totalCount; i++ {
		if first && i == 0 {
			task := &DailyTask{
				Uid:    cast.ToString(i + 1),
				Id:     1,
				Sender: 1007,
			}
			task.Target = []*Condition{
				{Type: condition.PROP, Id: 303, Num: 10},
			}
			task.Reward = c.GenReward()
			ary = append(ary, task)
			continue
		}
		rdIndex := ut.RandomIndexByWeight(taskBeans, func(item *cfg.DailyTask[int]) int { return item.Weight })
		taskBean := taskBeans[rdIndex]
		task := &DailyTask{
			Uid:    cast.ToString(i + 1),
			Id:     taskBean.Id,
			Target: make([]*Condition, 0),
			State:  daily_task_state.TAKE,
		}

		// 过滤出未完成任务的乘客
		availablePassengers := lo.Filter(allPassengers, func(p *Passenger, _ int) bool {
			return !array.Some(ary, func(t *DailyTask) bool { return t.Sender == p.Id })
		})

		// 优先选择已入住的乘客
		checkedInPassengers := lo.Filter(availablePassengers, func(p *Passenger, _ int) bool { return p.isCheckIn() })
		// 如果没有可用的乘客，则使用所有乘客
		if len(availablePassengers) == 0 {
			availablePassengers = allPassengers
		} else if len(checkedInPassengers) > 0 {
			// 如果有已签到的乘客，优先使用他们
			availablePassengers = checkedInPassengers
		}
		rdIndex = ut.Random(0, len(availablePassengers)-1)
		task.Sender = availablePassengers[rdIndex].Id
		task.Content = ut.Random(0, len(taskBean.Contents)-1)
		switch taskBean.Type {
		case daily_task_type.COLLECT:
			dailyTaskCfg := cfg.Misc_CContainer.GetObj().DailyTask
			cnt := ut.Random(dailyTaskCfg.TargetMinNum, dailyTaskCfg.TargetMaxNum)
			for i := 0; i < cnt; i++ {
				rdIndex := ut.RandomIndexByWeight(datas, func(task *cfg.DailyTaskItem[int]) int {
					return task.Weight
				})
				taskCfg := datas[rdIndex]
				// 任务目标
				convertTarget := ConfigConditionConvert(taskCfg.Target...)
				conds := convertTarget.All()
				for _, cond := range conds {
					cond.Num = ut.Random(taskCfg.Num.Min, taskCfg.Num.Max)
				}
				task.Target = MergeConditions(task.Target, conds)
				collectTargetMap[taskCfg.Id] = true
				if len(collectTargetMap) >= maxCount && len(datas) >= maxCount {
					datas = lo.Filter(datas, func(item *cfg.DailyTaskItem[int], index int) bool {
						return collectTargetMap[item.Id]
					})
				}
			}
		case daily_task_type.EQUIP:
			filterEquipAry := lo.Filter(cfg.EquipContainer.GetData(), func(data *cfg.Equip[int], _ int) bool {
				return data.RoleId == task.Sender && data.Index != 3
			})
			rdIndex := ut.Random(0, len(filterEquipAry)-1)
			data := filterEquipAry[rdIndex]
			// 任务目标
			cond := &Condition{Type: condition.EQUIP, Id: data.Id, Num: 1}
			task.Target = []*Condition{cond}
		case daily_task_type.DIALOG:
			canDoPlanets := lo.Filter(taskBean.RandomArg, func(item *cfg.ChestReward, index int) bool {
				if c.plr.PlanetData.GetPlanet(item.Id) == nil {
					return false
				}
				contains := lo.ContainsBy(ary, func(t *DailyTask) bool {
					if t.GetJson().Type != daily_task_type.DIALOG {
						return false
					}
					return t.Progress[0].Id == item.Id
				})
				return !contains
			})
			if len(canDoPlanets) == 0 {
				i--
				continue
			}
			rdIndex := ut.RandomIndexByWeight(canDoPlanets, func(randomObj *cfg.ChestReward) int { return randomObj.Weight })
			// 目标星球
			planetId := canDoPlanets[rdIndex].Id
			// 目标npc
			allNpc := cfg.NpcContainer.GetData()
			allNpc = lo.Filter(allNpc, func(item *cfg.Npc[int], index int) bool { return item.Planet == planetId && item.TaskDialog == 1 })
			rdIndex = ut.Random(0, len(allNpc)-1)
			npcDataId := allNpc[rdIndex].Id
			// 文案
			allDialog := cfg.DailyTaskDialogContainer.GetData()
			rdIndex = ut.Random(0, len(allDialog)-1)
			dialogDataId := allDialog[rdIndex].Id
			task.Progress = []*Condition{{Type: condition.TASK_DIALOG, Id: planetId, Extra: map[string]any{"npc": npcDataId, "dialog": dialogDataId}}}
			task.Target = []*Condition{{Type: condition.TASK_DIALOG, Id: planetId, Num: 1}}
		case daily_task_type.BATTLE:
			monsterAry := cfg.TowerMonsterContainer.GetData()
			unlockPlanets := c.plr.GetReachedPlanets()
			// 登陆过的 并且星球同时只能存在一个海盗
			unlockPlanets = lo.Filter(unlockPlanets, func(item *Planet, index int) bool {
				for _, t := range ary {
					if t.Planet == item.Id {
						return false
					}
				}
				return true
			})
			if len(unlockPlanets) == 0 {
				i--
				continue
			}
			rdIndex := ut.Random(0, len(unlockPlanets)-1)
			task.Planet = unlockPlanets[rdIndex].Id
			rdIndex = ut.Random(0, len(monsterAry)-1)
			data := monsterAry[rdIndex]
			node := c.plr.PlanetData.GetLastBattleNode(false)
			if node != nil {
				battleId := node.TypeId
				bean, _ := cfg.ChapterPlanetMonsterContainer.GetBeanByUnique(battleId)
				task.BattleInfo = lo.Map(bean.Monster, func(monster *cfg.Monster, _ int) *BattleRole {
					return &BattleRole{Id: monster.Id, Lv: monster.Level, StarLv: monster.StarLv}
				})
			} else {
				task.BattleInfo = lo.Map(data.Monster, func(m *cfg.Monster, i int) *BattleRole {
					return &BattleRole{Id: m.Id, Lv: 5}
				})
			}
		}

		task.Reward = c.GenReward()
		ary = append(ary, task)
	}
	return ary
}

func (c *DailyTaskModule) GenReward() []*Condition {
	rewardCfg := cfg.DailyTaskRewardContainer.GetData()[0]
	rewards := c.plr.GenerateRewards(rewardCfg.Reward, rewardCfg.RandomReward, &cfg.GenerateRewardsOpt{RandomCnt: rewardCfg.RandomCnt})
	return rewards
}

func (c *DailyTaskModule) Refresh() {
	c.BigGet = false
	c.Tasks = c.RandomDailyTask(!c.plr.isStepEndByUnlockFunc(function_type.PLAY_COLLECT))
	c.plr.Collect.RefreshMap()
}

func (c *DailyTaskModule) Unlock() { c.Refresh() }

func (c *DailyTaskModule) AddTaskProgress(taskUid string, targets []*Condition) {
	task := array.Find(c.Tasks, func(t *DailyTask) bool { return t.Uid == taskUid })
	task.AddProgress(targets)
}
