package cfg

import (
	"train/common/pb"
	ut "train/utils"
)

// TriggerConfig 成就触发器类型
type TriggerConfig struct {
	Type string `json:"type"`
	Id   any    `json:"id"`
}

// ConfigCondition
//
//	@Description: 条件配置
type ConfigCondition struct {
	Type    int `json:"type"`
	SubType int `json:"subType"`
	Id      any `json:"id"`
	Num     int `json:"num"`
	Weight  int `json:"weight"`
}

// OffConfig
//
//	@Description: 折扣配置
type OffConfig struct {
	Num    float64 `json:"num"`
	Weight int     `json:"weight"`
}

func (c *OffConfig) GetWeight() int {
	return c.Weight
}

type StoreOffConfig struct {
	Num    int `json:"num"`
	Weight int `json:"weight"`
}

func (s *StoreOffConfig) GetWeight() int { return s.Weight }

type FertilizerEffectConfig struct {
	Id     int `json:"id"`
	Effect int `json:"effect"`
}

type TalentConfig struct {
	MaxLevelBase int     `json:"maxLevelBase"`
	MaxLevelRate float64 `json:"maxLevelRate"`
}

type TransportConfig struct {
	Count    int `json:"count"`
	TimeRate int `json:"timeRate"`
	D0       int `json:"d0"`
	D1       int `json:"d1"`
	D2       int `json:"d2"`
	RareExp  int `json:"rareExp"`
}

type OreConfig struct {
	BreakMaxNum       int                `json:"breakMaxNum"`
	MonsterMinLevel   int                `json:"monsterMinLevel"`
	MonsterDepthRate  int                `json:"monsterDepthRate"`
	Treasure          []int              `json:"treasure"`
	TreasureValue     int                `json:"treasureValue"`
	TreasureValueMax  float64            `json:"treasureValueMax"`
	BossCondition     int                `json:"bossCondition"`
	GrayTreasureValue int                `json:"grayTreasureValue"`
	BlueTreasureValue int                `json:"blueTreasureValue"`
	NumWeight         []int              `json:"numWeight"`
	Terrian           []*ConfigCondition `json:"terrian"`
	UnlockAdd         int                `json:"unlockAdd"`
}

// GetTreasureType 矿洞特殊区域类型
func (o *OreConfig) GetTreasureType() pb.OreSpecialAreaType {
	return pb.OreSpecialAreaType(ut.RandomIndexByTotalWeight(o.Treasure))
}

// GetRandomOreNum 随机矿石的数量
func (o *OreConfig) GetRandomOreNum() int {
	return ut.RandomIndexByTotalWeight(o.NumWeight) + 1
}

type EquipMakeOutputCfg struct {
	Quality int `json:"quality"`
	Level   int `json:"level"`
	Weight  int `json:"weight"`
}

type DailyTaskConfig struct {
	TargetMinNum int            `json:"targetMinNum"`
	TargetMaxNum int            `json:"targetMaxNum"`
	Count        int            `json:"count"`
	BigReward    []*ChestReward `json:"bigReward"`
}

type CollectConfig struct {
	Map struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	} `json:"map"`
}

type ArrestConfig struct {
	Place []*ConfigCondition `json:"place"`
	Time  []*ConfigCondition `json:"time"`
}

type PvpCfg struct {
	TicketMax int          `json:"ticketMax"`
	Duration  int          `json:"duration"`
	Reward    []*RangeData `json:"reward"`
}

// RangeData 范围配置数据
type RangeData struct {
	Min int `json:"min"`
	Max int `json:"max"`
	Num int `json:"num"`
}

func (p *PvpCfg) GetRewardNum(sort int) int {
	for _, v := range p.Reward {
		if v.Max == -1 {
			return v.Num
		}
		if v.Min <= sort && sort <= v.Max {
			return v.Num
		}
	}
	return 0
}

type RandomCnt struct {
	Min int `json:"min"`
	Max int `json:"max"`
}

type GenerateRewardsOpt struct {
	RandomCnt *RandomCnt
}
