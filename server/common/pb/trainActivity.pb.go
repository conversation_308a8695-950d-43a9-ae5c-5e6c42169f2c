// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.4
// source: trainActivity.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// After are enums.
// After are structs.
// After are messages.
type C2S_GetTrainActivityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *C2S_GetTrainActivityMessage) Reset() {
	*x = C2S_GetTrainActivityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetTrainActivityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetTrainActivityMessage) ProtoMessage() {}

func (x *C2S_GetTrainActivityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetTrainActivityMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetTrainActivityMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{0}
}

type S2C_GetTrainActivityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
	Data []*TrainActivityItem `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty"`  //
}

func (x *S2C_GetTrainActivityMessage) Reset() {
	*x = S2C_GetTrainActivityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetTrainActivityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetTrainActivityMessage) ProtoMessage() {}

func (x *S2C_GetTrainActivityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetTrainActivityMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetTrainActivityMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{1}
}

func (x *S2C_GetTrainActivityMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *S2C_GetTrainActivityMessage) GetData() []*TrainActivityItem {
	if x != nil {
		return x.Data
	}
	return nil
}

type C2S_ArrangeTrainActivityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ary []int32 `protobuf:"varint,1,rep,packed,name=ary,proto3" json:"ary,omitempty"` //活动id列表
}

func (x *C2S_ArrangeTrainActivityMessage) Reset() {
	*x = C2S_ArrangeTrainActivityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_ArrangeTrainActivityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_ArrangeTrainActivityMessage) ProtoMessage() {}

func (x *C2S_ArrangeTrainActivityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_ArrangeTrainActivityMessage.ProtoReflect.Descriptor instead.
func (*C2S_ArrangeTrainActivityMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{2}
}

func (x *C2S_ArrangeTrainActivityMessage) GetAry() []int32 {
	if x != nil {
		return x.Ary
	}
	return nil
}

type S2C_ArrangeTrainActivityMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_ArrangeTrainActivityMessage) Reset() {
	*x = S2C_ArrangeTrainActivityMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_ArrangeTrainActivityMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_ArrangeTrainActivityMessage) ProtoMessage() {}

func (x *S2C_ArrangeTrainActivityMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_ArrangeTrainActivityMessage.ProtoReflect.Descriptor instead.
func (*S2C_ArrangeTrainActivityMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{3}
}

func (x *S2C_ArrangeTrainActivityMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type C2S_GetTrainActivityRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"` //序号
}

func (x *C2S_GetTrainActivityRewardMessage) Reset() {
	*x = C2S_GetTrainActivityRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *C2S_GetTrainActivityRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*C2S_GetTrainActivityRewardMessage) ProtoMessage() {}

func (x *C2S_GetTrainActivityRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use C2S_GetTrainActivityRewardMessage.ProtoReflect.Descriptor instead.
func (*C2S_GetTrainActivityRewardMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{4}
}

func (x *C2S_GetTrainActivityRewardMessage) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

type S2C_GetTrainActivityRewardMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"` //
}

func (x *S2C_GetTrainActivityRewardMessage) Reset() {
	*x = S2C_GetTrainActivityRewardMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_trainActivity_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *S2C_GetTrainActivityRewardMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S2C_GetTrainActivityRewardMessage) ProtoMessage() {}

func (x *S2C_GetTrainActivityRewardMessage) ProtoReflect() protoreflect.Message {
	mi := &file_trainActivity_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S2C_GetTrainActivityRewardMessage.ProtoReflect.Descriptor instead.
func (*S2C_GetTrainActivityRewardMessage) Descriptor() ([]byte, []int) {
	return file_trainActivity_proto_rawDescGZIP(), []int{5}
}

func (x *S2C_GetTrainActivityRewardMessage) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

var File_trainActivity_proto protoreflect.FileDescriptor

var file_trainActivity_proto_rawDesc = []byte{
	0x0a, 0x13, 0x74, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0c, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x1d, 0x0a, 0x1b, 0x43, 0x32,
	0x53, 0x5f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x5f, 0x0a, 0x1b, 0x53, 0x32, 0x43,
	0x5f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x33, 0x0a, 0x1f, 0x43, 0x32,
	0x53, 0x5f, 0x41, 0x72, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x05, 0x52, 0x03, 0x61, 0x72, 0x79, 0x22,
	0x35, 0x0a, 0x1f, 0x53, 0x32, 0x43, 0x5f, 0x41, 0x72, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x72,
	0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x39, 0x0a, 0x21, 0x43, 0x32, 0x53, 0x5f, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x22, 0x37, 0x0a, 0x21, 0x53, 0x32, 0x43, 0x5f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x69,
	0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x2f,
	0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_trainActivity_proto_rawDescOnce sync.Once
	file_trainActivity_proto_rawDescData = file_trainActivity_proto_rawDesc
)

func file_trainActivity_proto_rawDescGZIP() []byte {
	file_trainActivity_proto_rawDescOnce.Do(func() {
		file_trainActivity_proto_rawDescData = protoimpl.X.CompressGZIP(file_trainActivity_proto_rawDescData)
	})
	return file_trainActivity_proto_rawDescData
}

var file_trainActivity_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_trainActivity_proto_goTypes = []interface{}{
	(*C2S_GetTrainActivityMessage)(nil),       // 0: proto.C2S_GetTrainActivityMessage
	(*S2C_GetTrainActivityMessage)(nil),       // 1: proto.S2C_GetTrainActivityMessage
	(*C2S_ArrangeTrainActivityMessage)(nil),   // 2: proto.C2S_ArrangeTrainActivityMessage
	(*S2C_ArrangeTrainActivityMessage)(nil),   // 3: proto.S2C_ArrangeTrainActivityMessage
	(*C2S_GetTrainActivityRewardMessage)(nil), // 4: proto.C2S_GetTrainActivityRewardMessage
	(*S2C_GetTrainActivityRewardMessage)(nil), // 5: proto.S2C_GetTrainActivityRewardMessage
	(*TrainActivityItem)(nil),                 // 6: proto.TrainActivityItem
}
var file_trainActivity_proto_depIdxs = []int32{
	6, // 0: proto.S2C_GetTrainActivityMessage.data:type_name -> proto.TrainActivityItem
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_trainActivity_proto_init() }
func file_trainActivity_proto_init() {
	if File_trainActivity_proto != nil {
		return
	}
	file_struct_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_trainActivity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetTrainActivityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainActivity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetTrainActivityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainActivity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_ArrangeTrainActivityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainActivity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_ArrangeTrainActivityMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainActivity_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*C2S_GetTrainActivityRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_trainActivity_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*S2C_GetTrainActivityRewardMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_trainActivity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_trainActivity_proto_goTypes,
		DependencyIndexes: file_trainActivity_proto_depIdxs,
		MessageInfos:      file_trainActivity_proto_msgTypes,
	}.Build()
	File_trainActivity_proto = out.File
	file_trainActivity_proto_rawDesc = nil
	file_trainActivity_proto_goTypes = nil
	file_trainActivity_proto_depIdxs = nil
}
