[{"id": 1, "costTime": 240, "point": 1, "normal": {"num": 2}, "reward": [{"type": 11, "id": 25, "minNum": 10, "maxNum": 15}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 10, "weight": 30}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 2, "weight": 30}, {"type": 11, "id": 7, "num": 10, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 1, "num": -1, "weight": 40}, {"type": 2, "num": -1, "weight": 30}, {"type": 3, "num": -1, "weight": 30}]}, {"id": 2, "costTime": 300, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 20, "weight": 30}, {"type": 11, "id": 21, "minNum": 1, "maxNum": 3, "weight": 30}, {"type": 11, "id": 7, "num": 20, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 3, "costTime": 360, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 30, "weight": 30}, {"type": 11, "id": 21, "minNum": 2, "maxNum": 3, "weight": 30}, {"type": 11, "id": 7, "num": 30, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 4, "costTime": 420, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 40, "weight": 30}, {"type": 11, "id": 21, "minNum": 2, "maxNum": 4, "weight": 30}, {"type": 11, "id": 7, "num": 40, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 5, "costTime": 480, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 50, "weight": 30}, {"type": 11, "id": 21, "minNum": 3, "maxNum": 4, "weight": 30}, {"type": 11, "id": 7, "num": 50, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 6, "costTime": 540, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 60, "weight": 30}, {"type": 11, "id": 21, "minNum": 3, "maxNum": 5, "weight": 30}, {"type": 11, "id": 7, "num": 60, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}, {"id": 7, "costTime": 600, "point": 1, "normal": {"num": 3}, "reward": [{"type": 11, "id": 25, "minNum": 15, "maxNum": 20}], "randomCnt": {"min": 1, "max": 2}, "rewardRandom": [{"type": 1, "num": 70, "weight": 30}, {"type": 11, "id": 21, "minNum": 4, "maxNum": 5, "weight": 30}, {"type": 11, "id": 7, "num": 70, "weight": 30}, {"type": 11, "id": 701, "num": 1, "weight": 10}], "req": [{"type": 1, "id": 4, "num": -1, "weight": 0}, {"type": 1, "id": 3, "num": -1, "weight": 50}, {"type": 1, "id": 2, "num": -1, "weight": 20}, {"type": 1, "id": 1, "num": -1, "weight": 10}, {"type": 2, "num": -1, "weight": 10}, {"type": 3, "num": -1, "weight": 10}]}]