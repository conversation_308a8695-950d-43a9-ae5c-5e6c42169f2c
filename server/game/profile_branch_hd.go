package game

import (
	"train/base/cfg"
	"train/base/enum"
	"train/base/structs"
	"train/common/pb"
	ut "train/utils"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitProfile_branchHD 自动生成，不要在这个方法添加任何内容。
func InitProfile_branchHD(this *Game) {
	// 通过节点
	this.middleware.Wrap("C2S_ProfileBranchPassNodeMessage", this.C2sProfileBranchPassNodeMessageHandler)
	// 开始问答
	this.middleware.Wrap("C2S_ProfileBranchQuestionMessage", this.C2sProfileBranchQuestionMessageHandler)
	// 同步体力
	this.middleware.Wrap("C2S_ProfileBranchSyncEnergyMessage", this.C2sProfileBranchSyncEnergyMessageHandler)
	// 开启记忆阁
	this.middleware.Wrap("C2S_ProfileBranchUnlockMessage", this.C2sProfileBranchUnlockMessageHandler)
}
func (this *Game) C2sProfileBranchPassNodeMessageHandler(player *structs.Player, msg *pb.C2S_ProfileBranchPassNodeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ProfileBranchPassNodeMessage{Code: code}
	}
	nodeId := cast.ToInt(msg.GetNodeId())
	curLevel := player.ProfileBranch.GetCurLevel()
	curNodeId := curLevel.NodeId
	if nodeId != curNodeId {
		return send(1)
	}
	id := player.ProfileBranch.Id
	curNode := curLevel.GetNode(nodeId)
	player.ProfileBranch.PassNode(nodeId)
	player.GrantRewards(curNode.Reward, -1)
	if id == player.ProfileBranch.Id {
		return send(0)
	}
	return &pb.S2C_ProfileBranchPassNodeMessage{
		Code:  0,
		Level: player.ProfileBranch.GetNextLevel().ToPb(),
	}
	//@action-code-end
}
func (this *Game) C2sProfileBranchQuestionMessageHandler(player *structs.Player, msg *pb.C2S_ProfileBranchQuestionMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.ProfileBranch.UpdateEnergy()
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ProfileBranchQuestionMessage{Code: code}
	}
	if player.ProfileBranch.Energy <= 0 {
		return send(1)
	}
	player.ProfileBranch.ChangeEnergy(-1)
	return &pb.S2C_ProfileBranchQuestionMessage{
		Code:        0,
		Energy:      cast.ToInt32(player.ProfileBranch.Energy),
		SurplusTime: cast.ToInt32(player.ProfileBranch.GetEnergySurplusTime()),
	}
	//@action-code-end
}
func (this *Game) C2sProfileBranchSyncEnergyMessageHandler(player *structs.Player, msg *pb.C2S_ProfileBranchSyncEnergyMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.ProfileBranch.UpdateEnergy()
	return &pb.S2C_ProfileBranchSyncEnergyMessage{
		Code:        0,
		Energy:      cast.ToInt32(player.ProfileBranch.Energy),
		SurplusTime: cast.ToInt32(player.ProfileBranch.GetEnergySurplusTime()),
	}
	//@action-code-end
}
func (this *Game) C2sProfileBranchUnlockMessageHandler(player *structs.Player, msg *pb.C2S_ProfileBranchUnlockMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := int(msg.GetId())
	bean, _ := cfg.ProfileBranchLevelContainer.GetBeanById(id)
	if bean == nil {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 4}
	}
	branch := player.ProfileBranch
	if branch == nil || len(branch.Levels) == 0 {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 1}
	}
	if ut.IsIndexOutOfBounds(branch.Levels, id-1) {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 2}
	}
	level := branch.Levels[id-1]
	if level.Unlock {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 3}
	}
	req := structs.ConfigConditionConvert(bean.Request...).All()
	if len(player.CheckConditions(req)) > 0 {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 5}
	}
	// 检查固定条件
	// 1 上一关通关
	if branch.Id > 1 && branch.GetProgress(branch.Id-1) < 100 {
		return &pb.S2C_ProfileBranchUnlockMessage{Code: 5}
	}
	// 2 拥有全部记忆乘客
	for _, node := range level.Nodes {
		if node.Type != enum.QUESTION {
			continue
		}
		for _, v := range node.Reward {
			bean, _ := cfg.CharacterProfileContainer.GetBeanById(cast.ToInt(v.Id))
			if bean == nil {
				continue
			}
			if player.GetPassengerById(bean.CharacterId) == nil {
				return &pb.S2C_ProfileBranchUnlockMessage{Code: 5}
			}
		}
	}
	level.Unlock = true
	return &pb.S2C_ProfileBranchUnlockMessage{}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
