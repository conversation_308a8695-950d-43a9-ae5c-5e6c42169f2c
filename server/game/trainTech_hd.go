package game

import (
	"fmt"
	"train/base/cfg"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	"github.com/samber/lo"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTrainTechHD 自动生成，不要在这个方法添加任何内容。
func InitTrainTechHD(this *Game) {
	// 升级列车科技
	this.middleware.Wrap("C2S_TrainTechUpgradeMessage", this.C2sTrainTechUpgradeMessageHandler)
}
func (this *Game) C2sTrainTechUpgradeMessageHandler(player *structs.Player, msg *pb.C2S_TrainTechUpgradeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	techId := msg.GetId()
	lv := player.Tech.GetTechLevel(techId)
	tech := cfg.TrainTechLevelContainer.GetBean(fmt.Sprintf("%s-%d", techId, lv+1))
	if tech == nil {
		return &pb.S2C_TrainTechUpgradeMessage{Code: 1}
	}
	costs := structs.ConfigConditionConvert(tech.Cost...).All()
	failed := player.CheckConditions(costs)
	if len(failed) > 0 {
		return &pb.S2C_TrainTechUpgradeMessage{Code: 2}
	}
	techCfg := cfg.TrainTechContainer.GetBean(techId)
	if len(techCfg.Pre) > 0 {
		cnt := lo.SumBy(techCfg.Pre, func(item string) int { return lo.If(player.Tech.GetTechLevel(item) > 0, 1).Else(0) })
		if cnt == 0 {
			return &pb.S2C_TrainTechUpgradeMessage{Code: 3}
		}
	}

	player.DeductCosts(costs, ta.ResChangeSceneTypeNoReport)
	player.Tech.Upgrade(techId)

	return &pb.S2C_TrainTechUpgradeMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
