package game

import (
	"fmt"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/data/item_id"
	"train/base/enum"
	"train/base/event"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"
	"train/utils/array"
	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitPlanetHD 自动生成，不要在这个方法添加任何内容。
func InitPlanetHD(this *Game) {
	// 申请航行至某个星球
	this.middleware.Wrap("C2S_TrainNavigationMessage", this.C2sTrainNavigationMessageHandler)
	// 获取星球航行剩余时间
	this.middleware.Wrap("C2S_GetPlanetMoveSurplusTimeMessage", this.C2sGetPlanetMoveSurplusTimeMessageHandler)
	// 登陆星球
	this.middleware.Wrap("C2S_LandPlanetMessage", this.C2sLandPlanetMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassMessage", this.C2sChapterPassMessageHandler)
	// 通过星球支线节点
	this.middleware.Wrap("C2S_PassBranchPlanetNodeMessage", this.C2sPassBranchPlanetNodeMessageHandler)
	// 领取星球节点内的奖励
	this.middleware.Wrap("C2S_ClaimBranchPlanetNodeRewardMessage", this.C2sClaimBranchPlanetNodeRewardMessageHandler)
	// 取消航行
	this.middleware.Wrap("C2S_CancelMoveToPlanetMessage", this.C2sCancelMoveToPlanetMessageHandler)
	// 解锁星球贴纸
	this.middleware.Wrap("C2S_UnlockProfileMessage", this.C2sUnlockProfileMessageHandler)
	// 领取星球贴纸奖励
	this.middleware.Wrap("C2S_ProfileCollectRewardMessage", this.C2sProfileCollectRewardMessageHandler)
	// 星球资料排序更换
	this.middleware.Wrap("C2S_PlanetProfileSortChangeMessage", this.C2sPlanetProfileSortChangeMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassRandomBoxMessage", this.C2sChapterPassRandomBoxMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterStartTimeLimitBoxMessage", this.C2sChapterStartTimeLimitBoxMessageHandler)
	// 请求同步时间
	this.middleware.Wrap("C2S_ChapterSyncTimeLimitBoxMessage", this.C2sChapterSyncTimeLimitBoxMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassTimeLimitBoxMessage", this.C2sChapterPassTimeLimitBoxMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassMonsterBoxMessage", this.C2sChapterPassMonsterBoxMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassToolBlessMessage", this.C2sChapterPassToolBlessMessageHandler)
	// 请求通过关卡节点
	this.middleware.Wrap("C2S_ChapterPassRageModeMessage", this.C2sChapterPassRageModeMessageHandler)
	// 进行宣传
	this.middleware.Wrap("C2S_DoPublicityMessage", this.C2sDoPublicityMessageHandler)
	// 领取宣传奖励
	this.middleware.Wrap("C2S_GetPublicityRewardMessage", this.C2sGetPublicityRewardMessageHandler)
}
func (this *Game) C2sTrainNavigationMessageHandler(player *structs.Player, msg *pb.C2S_TrainNavigationMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	id := cast.ToInt(msg.PlanetId)
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_TrainNavigationResultMessage{Code: cast.ToInt32(code)}
	}
	Planet := player.PlanetData
	if Planet.IsMove() {
		return send(1) //正在航行中,无法继续
	}
	if Planet.CurPlanetId == id {
		return send(2) // 当前星球 无需航行
	}
	// 获取配置
	_, exists := cfg.PlanetContainer.GetBeanById(id)
	if !exists {
		return send(3) // 星球id错误,找不到配置
	}
	planet := Planet.GetPlanet(id)
	if planet == nil {
		return send(4) //没解锁
	}
	if !msg.GetPlus() {
		player.MoveToPlanet(id)
	} else {
		curPlanetId := Planet.CurPlanetId
		bean, _ := cfg.PlanetMoveTimeContainer.GetBeanById(curPlanetId)
		costTime := bean.Time[id]
		unit := cfg.Misc_CContainer.GetObj().PlanetMoveTimeToStoneUnit
		costItemNum := costTime / unit
		cost := &structs.Condition{Type: condition.PROP, Id: item_id.TrainMoveStone, Num: costItemNum}
		if !player.CheckCondition(cost) {
			return send(5) // 道具不足
		}
		player.PlanetData.ReachPlanet(id)
		player.DeductCost(cost, ta.ResChangeSceneTypeNoReport)
	}

	return &pb.S2C_TrainNavigationResultMessage{Code: 0, Time: int32(Planet.GetMoveSurplusTime())}
	//@action-code-end
}
func (this *Game) C2sGetPlanetMoveSurplusTimeMessageHandler(player *structs.Player, msg *pb.C2S_GetPlanetMoveSurplusTimeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	player.UpdateSpeedUp()
	player.UpdateMoveToPlanet()
	// 有运送任务，检查运送目的地和状态 是否可以完成
	player.Transport.CheckPlanetMoveDone()

	time := player.PlanetData.GetMoveSurplusTime()
	return &pb.S2C_GetPlanetMoveSurplusTimeRespMessage{Code: 0, Time: cast.ToInt32(time)}
	//@action-code-end
}
func (this *Game) C2sLandPlanetMessageHandler(player *structs.Player, msg *pb.C2S_LandPlanetMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_LandPlanetRespMessage{
			Code: cast.ToInt32(code),
		}
	}
	player.UpdateMoveToPlanet()
	Planet := player.PlanetData
	if Planet.IsMove() {
		return send(1)
	}

	player.LandPlanet()
	return send(0)
	//@action-code-end
}
func (this *Game) C2sChapterPassMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassResultMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPass")
	if code != 0 {
		return send(code)
	}
	player.PassPlanetCurNode(planet)
	//发奖
	rewards := player.PlanetData.GetPlanetNodeRewards(data)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeChapterPass)

	next := planet.CurNodeId
	nextMapId := planet.MapId
	return &pb.S2C_ChapterPassResultMessage{
		Code:    cast.ToInt32(0),
		NextId:  cast.ToInt32(next),
		MapId:   cast.ToInt32(nextMapId),
		Rewards: structs.ToPbConditions(rewards),
	}
	//@action-code-end
}
func (this *Game) C2sPassBranchPlanetNodeMessageHandler(player *structs.Player, msg *pb.C2S_PassBranchPlanetNodeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	branchId, mapId, nodeId := msg.GetBranchId(), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_PassBranchPlanetNodeMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, branch, data := checkBranchNodeCommon(player, branchId, mapId, nodeId, "PassBranchNode")
	if code != 0 {
		return send(code)
	}
	//发奖
	rewards := player.PlanetData.GetPlanetNodeRewards(data)
	if len(branch.NodeRewards) > 0 {
		rewards = lo.Filter(rewards, func(_ *structs.Condition, index int) bool {
			return !array.Has(branch.NodeRewards, index)
		})
	}
	player.PassBranchPlanetNode(branch)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeBranchPass)
	next := branch.NodeId
	nextMapId := branch.MapId
	return &pb.S2C_PassBranchPlanetNodeMessage{
		Code:    cast.ToInt32(0),
		NextId:  cast.ToInt32(next),
		MapId:   cast.ToInt32(nextMapId),
		Rewards: structs.ToPbConditions(rewards),
	}
	//@action-code-end
}
func (this *Game) C2sClaimBranchPlanetNodeRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimBranchPlanetNodeRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	branchId, mapId, nodeId := msg.GetBranchId(), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ClaimBranchPlanetNodeRewardMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, branch, data := checkBranchNodeCommon(player, branchId, mapId, nodeId, "ClaimBranchNodeReward")
	if code != 0 {
		return send(code)
	}

	if data.Type != enum.NONE {
		return send(4)
	}

	bean, _ := cfg.BranchPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(5)
	}

	if len(bean.RewardPoints) <= nodeId || len(bean.Rewards) <= nodeId {
		return send(6)
	}

	nodeRewards := branch.NodeRewards
	if array.Has(nodeRewards, nodeId) {
		return send(7)
	}

	nodeRewards = append(nodeRewards, nodeId)
	branch.NodeRewards = nodeRewards

	//发奖
	reward := player.ChestRewardToCondition(bean.Rewards[nodeId])
	player.GrantReward(reward, ta.ResChangeSceneTypeChapterPass)

	return send(0)
	//@action-code-end
}
func (this *Game) C2sCancelMoveToPlanetMessageHandler(player *structs.Player, msg *pb.C2S_CancelMoveToPlanetMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_CancelMoveToPlanetMessage{
			Code: code,
		}
	}
	Planet := player.PlanetData
	if !Planet.IsMove() {
		return send(1)
	}
	Planet.CancelMove()
	return send(0)
	//@action-code-end
}
func (this *Game) C2sUnlockProfileMessageHandler(player *structs.Player, msg *pb.C2S_UnlockProfileMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId := int(msg.GetPlanetId())
	profileId := int(msg.GetId())

	if !lo.Contains(player.PlanetProfiles, profileId) {
		return &pb.S2C_UnlockProfileMessage{Code: 1}
	}

	planet := player.PlanetData.GetPlanet(planetId)
	if planet == nil {
		return &pb.S2C_UnlockProfileMessage{Code: 2}
	}

	bean, _ := cfg.PlanetProfileContainer.GetBeanById(profileId)
	if bean == nil {
		return &pb.S2C_UnlockProfileMessage{Code: 3}
	}
	index := int(msg.GetIndex())
	validIndex := index == 1
	// 贴纸的顺序不固定
	if bean.Type == 1 {
		validIndex = index == 1 || index == 2
	}
	if !validIndex {
		return &pb.S2C_UnlockProfileMessage{Code: 4}
	}
	_, ok := planet.ProfileData[profileId]
	// 已经解锁？
	if ok {
		return &pb.S2C_UnlockProfileMessage{Code: 5}
	}
	for profileId, i := range planet.ProfileData {
		tmpBean, _ := cfg.PlanetProfileContainer.GetBeanById(profileId)
		if tmpBean == nil {
			continue
		}
		if tmpBean.Type == bean.Type && tmpBean.Area == bean.Area && i == index {
			return &pb.S2C_UnlockProfileMessage{Code: 6}
		}
	}

	player.RemovePlanetProfile(profileId)
	planet.ProfileData[profileId] = index
	player.GetEvent().Emit(event.PlanetProfileUnlock)
	return &pb.S2C_UnlockProfileMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sProfileCollectRewardMessageHandler(player *structs.Player, msg *pb.C2S_ProfileCollectRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planet := player.PlanetData.GetPlanet(int(msg.GetPlanetId()))
	if planet == nil {
		return &pb.S2C_ProfileCollectRewardMessage{Code: 1}
	}
	area := int(msg.GetArea())
	ary := cfg.PlanetProfileContainer.GetData()
	// 指定星球 指定区域
	ary = lo.Filter(ary, func(item *cfg.PlanetProfile[int], _ int) bool {
		return item.PlanetId == planet.Id && item.Area == area
	})
	if len(ary) == 0 {
		return &pb.S2C_ProfileCollectRewardMessage{Code: 2}
	}
	// 进度
	step := int(msg.GetStep())
	keys := lo.Keys(planet.ProfileData)
	ary = lo.Filter(ary, func(item *cfg.PlanetProfile[int], _ int) bool { return lo.Contains(keys, item.Id) })

	if len(ary) < step {
		return &pb.S2C_ProfileCollectRewardMessage{Code: 3}
	}
	if planet.IsProfileCollectRewardGet(area, step) {
		return &pb.S2C_ProfileCollectRewardMessage{Code: 4}
	}
	bean, _ := cfg.PlanetProfileRewardContainer.GetBeanByUnique(fmt.Sprintf("%d-%d", planet.Id, area))
	if bean == nil {
		// 区域奖励不存在？
		return &pb.S2C_ProfileCollectRewardMessage{Code: 5}
	}
	rewards := lo.Filter(bean.Reward, func(item *cfg.ChestReward, _ int) bool { return item.Group == step })
	if len(rewards) == 0 {
		// step 有错误  没有对应的奖励配置
		return &pb.S2C_ProfileCollectRewardMessage{Code: 6}
	}

	player.GrantRewards(player.GenerateRewards(rewards, nil), ta.ResChangeSceneTypeNoReport)
	planet.ProfileCollectReward = append(planet.ProfileCollectReward, area)
	planet.ProfileCollectReward = append(planet.ProfileCollectReward, step)
	return &pb.S2C_ProfileCollectRewardMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sPlanetProfileSortChangeMessageHandler(player *structs.Player, msg *pb.C2S_PlanetProfileSortChangeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId := int(msg.GetPlanetId())
	sort := msg.GetSort()
	planet := player.PlanetData.GetPlanet(planetId)
	if planet == nil {
		return &pb.S2C_PlanetProfileSortChangeMessage{Code: 1}
	}

	for id, pos := range sort {
		profileId := int(id)
		// 检查解锁情况
		_, ok := planet.ProfileData[profileId]
		if !ok {
			return &pb.S2C_PlanetProfileSortChangeMessage{Code: 2}
		}
		// 检查顺序位置合理
		bean, _ := cfg.PlanetProfileContainer.GetBeanById(profileId)
		if bean == nil {
			return &pb.S2C_PlanetProfileSortChangeMessage{Code: 3}
		}
		if bean.Type == 1 {
			if pos != 1 && pos != 2 {
				return &pb.S2C_PlanetProfileSortChangeMessage{Code: 4}
			}
		} else if pos != 1 {
			return &pb.S2C_PlanetProfileSortChangeMessage{Code: 5}
		}
	}

	for id, pos := range sort {
		profileId := int(id)
		planet.ProfileData[profileId] = int(pos)
	}

	return &pb.S2C_PlanetProfileSortChangeMessage{Code: 0}
	//@action-code-end
}
func (this *Game) C2sChapterPassRandomBoxMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassRandomBoxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassRandomBoxMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPassRandomBox")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	diyRewards := bean.DiyRewards
	rewards := player.GenRandomBoxRewards(diyRewards[0].Id)

	player.PassPlanetCurNode(planet)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeChapterPass)

	next := planet.CurNodeId
	nextMapId := planet.MapId
	return &pb.S2C_ChapterPassRandomBoxMessage{
		Code:    cast.ToInt32(0),
		NextId:  cast.ToInt32(next),
		MapId:   cast.ToInt32(nextMapId),
		Rewards: structs.ToPbConditions(rewards),
	}
	//@action-code-end
}
func (this *Game) C2sChapterStartTimeLimitBoxMessageHandler(player *structs.Player, msg *pb.C2S_ChapterStartTimeLimitBoxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterStartTimeLimitBoxMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterStartTimeLimitBox")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	nodeData := planet.NodeData
	endTime, ok := nodeData["endTime"]
	if !ok {
		time := bean.Time * ut.TIME_SECOND
		endTime = player.GetNowTime() + time
		nodeData["endTime"] = endTime
	}
	time := cast.ToInt(endTime) - player.GetNowTime()
	return &pb.S2C_ChapterStartTimeLimitBoxMessage{
		Code:        cast.ToInt32(0),
		SurplusTime: cast.ToInt32(time),
	}
	//@action-code-end
}
func (this *Game) C2sChapterSyncTimeLimitBoxMessageHandler(player *structs.Player, msg *pb.C2S_ChapterSyncTimeLimitBoxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterSyncTimeLimitBoxMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterSyncTimeLimitBox")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	clicks := int(msg.GetClicks())
	nodeData := planet.NodeData
	endTime := cast.ToInt(nodeData["endTime"])
	surplusTime := 0
	rewards := make([]*structs.Condition, 0)
	state := 0
	if endTime > 0 {
		state = 1
	}
	if state == 1 {
		surplusTime = endTime - player.GetNowTime()
		surplusTime = ut.Max(surplusTime, 0)
		num := cast.ToInt(nodeData["num"])
		add := bean.DiyRewards[0].Num * clicks
		num = ut.Min(num+add, bean.MaxRewardNum)
		nodeData["num"] = num
		if surplusTime <= 0 {
			state = 2
		}
		if num > 0 {
			rewards = []*structs.Condition{
				{
					Type: bean.DiyRewards[0].Type,
					Id:   cast.ToInt(bean.DiyRewards[0].Id),
					Num:  num,
				},
			}
		}
	}
	return &pb.S2C_ChapterSyncTimeLimitBoxMessage{
		Code:        cast.ToInt32(0),
		State:       cast.ToInt32(state),
		SurplusTime: cast.ToInt32(surplusTime),
		Rewards:     structs.ToPbConditions(rewards),
	}
	//@action-code-end
}
func (this *Game) C2sChapterPassTimeLimitBoxMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassTimeLimitBoxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassTimeLimitBoxMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPassTimeLimitBox")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	nodeData := planet.NodeData
	num := cast.ToInt(nodeData["num"])
	rewards := make([]*structs.Condition, 0)
	if num > 0 {
		rewards = []*structs.Condition{
			{
				Type: bean.DiyRewards[0].Type,
				Id:   cast.ToInt(bean.DiyRewards[0].Id),
				Num:  num,
			},
		}
	}
	planet.PassCurNode()
	player.GrantRewards(rewards, ta.ResChangeSceneTypeChapterPass)
	return &pb.S2C_ChapterPassTimeLimitBoxMessage{
		Code: cast.ToInt32(0),
	}
	//@action-code-end
}
func (this *Game) C2sChapterPassMonsterBoxMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassMonsterBoxMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	num := int(msg.GetNum())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassMonsterBoxMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPassMonsterBox")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	diyRewards := bean.DiyRewards
	num = ut.Min(num*cast.ToInt(diyRewards[0].Num), bean.MaxRewardNum)
	rewards := []*structs.Condition{
		{
			Type: diyRewards[0].Type,
			Id:   cast.ToInt(diyRewards[0].Id),
			Num:  num,
		},
	}
	player.PassPlanetCurNode(planet)
	player.GrantRewards(rewards, ta.ResChangeSceneTypeChapterPass)

	return &pb.S2C_ChapterPassMonsterBoxMessage{
		Code:    cast.ToInt32(0),
		Rewards: structs.ToPbConditions(rewards),
	}
	//@action-code-end
}
func (this *Game) C2sChapterPassToolBlessMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassToolBlessMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassToolBlessMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPassToolBless")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	player.PassPlanetCurNode(planet)
	player.Tool.BlessId = bean.Id
	player.Tool.ChangeBlessCount(bean.Count)
	return &pb.S2C_ChapterPassToolBlessMessage{
		Code: cast.ToInt32(0),
	}
	//@action-code-end
}
func (this *Game) C2sChapterPassRageModeMessageHandler(player *structs.Player, msg *pb.C2S_ChapterPassRageModeMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId, mapId, nodeId := cast.ToInt(msg.GetPlanetId()), cast.ToInt(msg.GetMapId()), cast.ToInt(msg.GetNodeId())
	send := func(code int) protoreflect.ProtoMessage {
		return &pb.S2C_ChapterPassToolBlessMessage{
			Code: cast.ToInt32(code),
		}
	}
	code, planet, data := checkPlanetNodeCommon(player, planetId, mapId, nodeId, "ChapterPassToolBless")
	if code != 0 {
		return send(code)
	}
	bean, _ := cfg.ChapterPlanetSpContainer.GetBeanByUnique(data.TypeId)
	if bean == nil {
		return send(4)
	}
	player.PassPlanetCurNode(planet)

	mapData := planet.GetJson().GetMapData(planet.MapId)
	count := ut.Min(len(mapData)-nodeId, bean.Count)

	for i := 1; i <= count; i++ {
		data := mapData[nodeId+i]
		rewards := player.PlanetData.GetPlanetNodeRewards(data)
		player.PassPlanetCurNode(planet)
		player.GrantRewards(rewards, ta.ResChangeSceneTypeChapterPass)
	}

	// misc := cfg.GetMisc()
	// mapData := planet.GetJson().GetMapData(planet.MapId)
	// endId := ut.Min(len(mapData), nodeId+bean.Count)
	// endData := mapData[endId]
	// if endData == nil {
	// 	return send(5)
	// }
	// speed := misc.Character.RageModeRate * float64(misc.Character.MoveSpeed)
	// dis := endData.Pos.Sub(data.Pos).Mag()
	// time := int(dis / speed * ut.TIME_SECOND)
	return &pb.S2C_ChapterPassToolBlessMessage{
		Code: cast.ToInt32(0),
	}
	//@action-code-end
}
func (this *Game) C2sDoPublicityMessageHandler(player *structs.Player, msg *pb.C2S_DoPublicityMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId := int(msg.GetPlanetId())
	planet := player.PlanetData.GetPlanet(planetId)
	if planet == nil || !planet.IsDone() {
		return &pb.S2C_DoPublicityMessage{Code: 1}
	}
	next := planet.GetPublicityNextInfo()
	cost := &structs.Condition{Type: condition.PROP, Id: item_id.PUBLICITY, Num: next.Cost}
	if !player.CheckCondition(cost) {
		return &pb.S2C_DoPublicityMessage{Code: 2}
	}
	num := ut.Random(next.Min, next.Max)
	if num > 0 && planet.RoleNum == 0 {
		planet.PublicityUnGetOutputTime = 0
	}
	cur := planet.GetPublicityInfo()
	planet.RoleNum += num
	newCur := planet.GetPublicityInfo()
	var reward *structs.Condition
	if cur != newCur {
		planet.RoleNum -= num
		reward = planet.GetPublicityReward()
		planet.RoleNum += num
	}
	player.DeductCost(cost, ta.ResChangeSceneTypeNoReport)
	r := &pb.S2C_DoPublicityMessage{Code: 0, Num: int32(num), PublicityUnGetOutputTime: int32(planet.PublicityUnGetOutputTime)}
	if reward != nil {
		r.Rewards = []*pb.Condition{reward.ToPb()}
	}
	return r
	//@action-code-end
}
func (this *Game) C2sGetPublicityRewardMessageHandler(player *structs.Player, msg *pb.C2S_GetPublicityRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	planetId := int(msg.GetPlanetId())
	rewards := make([]*structs.Condition, 0)
	if planetId != -1 {
		planet := player.PlanetData.GetPlanet(planetId)
		if planet == nil {
			return &pb.S2C_GetPublicityRewardMessage{Code: 1}
		}
		reward := planet.GetPublicityReward()
		if reward == nil {
			return &pb.S2C_GetPublicityRewardMessage{Code: 2}
		}
		rewards = append(rewards, reward)
	} else {
		for _, p := range player.PlanetData.Planets {
			r := p.GetPublicityReward()
			if r == nil {
				continue
			}
			rewards = append(rewards, r)
		}
		if len(rewards) == 0 {
			return &pb.S2C_GetPublicityRewardMessage{Code: 2}
		}
	}
	timeMap := make(map[int32]int32)
	for _, p := range player.PlanetData.Planets {
		timeMap[int32(p.Id)] = int32(p.PublicityUnGetOutputTime)
	}
	r := &pb.S2C_GetPublicityRewardMessage{Code: 0, TimeMap: timeMap}
	if len(rewards) > 0 {
		r.Rewards = structs.ToPbConditions(rewards)
	}
	return r
	//@action-code-end
}

// @logic-code-start 自定义代码必须放在start和end之间
// checkPlanetNodeCommon 用于校验星球节点的通用逻辑，减少重复代码
// 返回值：code（0为通过，非0为错误码），*cfg.MapBase（节点数据）
func checkPlanetNodeCommon(player *structs.Player, planetId, mapId, nodeId int, logPrefix string) (code int, planet *structs.Planet, data *cfg.MapBase) {
	planet = player.PlanetData.GetCurPlanet()
	if planet == nil {
		return 1, nil, nil
	}
	if planet.Id != planetId || planet.MapId != mapId || planet.CurNodeId != nodeId {
		log.Error("[%s] %s 和当前节点不匹配, 当前: %d-%d-%d, 参数: %d-%d-%d", player.GetUid(), logPrefix, planet.Id, planet.MapId, planet.CurNodeId, planetId, mapId, nodeId)
		return 2, planet, nil
	}
	data = planet.GetMapDataById(nodeId)
	if data == nil {
		log.Error("[%s] %s 找不到节点配置:%d,%d,%d,%d", player.GetUid(), logPrefix, planetId, mapId, nodeId)
		return 3, planet, nil
	}
	return 0, planet, data
}

// checkBranchNodeCommon 用于校验支线节点的通用逻辑，减少重复代码
// 返回值：code（0为通过，非0为错误码），*structs.PlanetBranch，*cfg.MapBase
func checkBranchNodeCommon(player *structs.Player, branchId string, mapId, nodeId int, logPrefix string) (code int, branch *structs.PlanetBranch, data *cfg.MapBase) {
	branchCfg, exists := cfg.PlanetBranchContainer.GetBeanByUnique(branchId)
	if !exists {
		return 1, nil, nil
	}
	planet := player.PlanetData.GetCurPlanet()
	if planet.Id != branchCfg.PlanetId {
		return 2, nil, nil
	}
	branch = planet.GetBranch(branchId)
	if branch.MapId != mapId || branch.NodeId != nodeId {
		log.Error("[%s] %s 和当前节点不匹配, 当前: %s-%d-%d, 参数: %d-%d", player.GetUid(), logPrefix, branch.Id, branch.MapId, branch.NodeId, mapId, nodeId)
		return 2, branch, nil
	}
	data = branch.GetNodeData(mapId, nodeId)
	if data == nil {
		log.Error("[%s] %s 找不到节点配置:%s,%d,%d", player.GetUid(), logPrefix, branch.Id, mapId, nodeId)
		return 3, branch, nil
	}
	return 0, branch, data
}

//@logic-code-end
