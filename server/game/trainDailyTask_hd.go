package game

import (
	"sort"
	"train/base/enum/wanted_condition_type"
	"train/base/structs"
	"train/common/pb"
	"train/common/ta"
	ut "train/utils"

	"github.com/huyangv/vmqant/log"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"google.golang.org/protobuf/reflect/protoreflect"
)

// InitTrainDailyTaskHD 自动生成，不要在这个方法添加任何内容。
func InitTrainDailyTaskHD(this *Game) {
	// 同步列车日常任务数据
	this.middleware.Wrap("C2S_SyncAllTrainDailyTaskMessage", this.C2sSyncAllTrainDailyTaskMessageHandler)
	// 开始列车日常任务
	this.middleware.Wrap("C2S_StartTrainDailyTaskMessage", this.C2sStartTrainDailyTaskMessageHandler)
	// 领取列车日常任务奖励
	this.middleware.Wrap("C2S_ClaimTrainDailyTaskRewardMessage", this.C2sClaimTrainDailyTaskRewardMessageHandler)
}
func (this *Game) C2sSyncAllTrainDailyTaskMessageHandler(player *structs.Player, msg *pb.C2S_SyncAllTrainDailyTaskMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	index := int(msg.GetIndex())
	if index == -1 {
		return &pb.S2C_SyncAllTrainDailyTaskMessage{Data: player.TrainDailyTask.ToPb().List}
	}
	list := player.TrainDailyTask.List
	if ut.IsIndexOutOfBounds(list, index) {
		return &pb.S2C_SyncAllTrainDailyTaskMessage{Code: 1}
	}
	return &pb.S2C_SyncAllTrainDailyTaskMessage{Data: []*pb.TrainDailyTaskItem{list[index].ToPb()}}
	//@action-code-end
}
func (this *Game) C2sStartTrainDailyTaskMessageHandler(player *structs.Player, msg *pb.C2S_StartTrainDailyTaskMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_StartTrainDailyTaskMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	roles := ut.ToInt(msg.GetRoles())
	trainDailyTask := player.TrainDailyTask.Get(index)
	if trainDailyTask == nil {
		return send(1)
	}
	if trainDailyTask.State != pb.CommonState_NotStart {
		return send(2)
	}
	err_role := false
	roleAry := lo.Map(roles, func(id int, i int) *structs.Passenger {
		p := player.GetPassengerById(id)
		if p == nil {
			err_role = true
		} else if _, exists := lo.Find(player.TrainDailyTask.List, func(w *structs.TrainDailyTaskItem) bool {
			// 乘客同时只能进行一个工作
			return w != trainDailyTask && w.State == pb.CommonState_NotStart && lo.Contains(w.Roles, id)
		}); exists {
			err_role = true
		} else if _, exists := lo.Find(player.BurstTask.List, func(w *structs.BurstTaskItem) bool {
			// 突发任务
			return w.State == pb.CommonState_NotStart && lo.Contains(w.Roles, id)
		}); exists {
			err_role = true
		}
		return p
	})
	if err_role {
		log.Error("[%s] C2sStartWantedMessageHandler role not found: %v", roles)
		return send(7)
	}

	if len(roleAry) < trainDailyTask.People {
		return send(6)
	}

	sort.Slice(trainDailyTask.Conditions, func(i, j int) bool {
		a := trainDailyTask.Conditions[i]
		b := trainDailyTask.Conditions[j]
		if a.Type != b.Type {
			return a.Type < b.Type
		}
		return a.Value > b.Value
	})
	qualityUsed := make(map[*structs.Passenger]int)
	filterCondAry := make([]*structs.WantedCondition, 0)
	cnt := len(trainDailyTask.Conditions)
	for _, role := range roleAry {
		for _, condition := range trainDailyTask.Conditions {
			if lo.Contains(filterCondAry, condition) {
				continue
			}
			switch condition.Type {
			case wanted_condition_type.QUALITY:
				if qualityUsed[role] > 0 {
					continue
				}
				if role.GetQuality() >= condition.Value {
					qualityUsed[role] = 1
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.ANIMAL_TYPE:
				if role.GetJson().AnimalType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			case wanted_condition_type.BATTLE_TYPE:
				if role.GetJson().BattleType == condition.Value {
					cnt--
					filterCondAry = append(filterCondAry, condition)
				}
			}
		}
	}
	if cnt > 0 {
		return send(6)
	}
	player.TrainDailyTask.Start(index, roles)
	return send(0)
	//@action-code-end
}
func (this *Game) C2sClaimTrainDailyTaskRewardMessageHandler(player *structs.Player, msg *pb.C2S_ClaimTrainDailyTaskRewardMessage) protoreflect.ProtoMessage {
	//@action-code-start 所有handler逻辑代码必须在start和end之间
	send := func(code int32) protoreflect.ProtoMessage {
		return &pb.S2C_ClaimTrainDailyTaskRewardMessage{Code: code}
	}
	index := cast.ToInt(msg.GetIndex())
	item := player.TrainDailyTask.Get(index)
	if item == nil {
		return send(1)
	}
	if !item.CanComplete() {
		return send(2)
	}
	player.TrainDailyTask.Complete(index)
	player.GrantRewards(item.Rewards, ta.ResChangeSceneTypeNoReport)
	return &pb.S2C_ClaimTrainDailyTaskRewardMessage{Code: 0}
	//@action-code-end
}

//@logic-code-start 自定义代码必须放在start和end之间

//@logic-code-end
