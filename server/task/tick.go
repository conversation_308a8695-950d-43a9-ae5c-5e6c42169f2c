package task

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"
	"train/base/cfg"
	"train/base/data/condition"
	"train/base/manager"
	"train/base/structs"
	"train/db"

	"github.com/bamzi/jobrunner"
	"github.com/huyangv/vmqant/log"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var isRunning = false

const (
	title      = "竞技场奖励"
	season_day = 1 // 赛季时长 方便测试
)

func (t *Task) RunTick() {
	isRunning = true
	t.GoogleOrderRefundCheck()

	// 这个库使用标准的cron表达式格式
	// 包含5个字段:
	// 分钟 小时 日期 月份 星期
	err := jobrunner.Schedule("0 5 * * *", &NewDayTicker{taskModule: t})

	if err != nil {
		log.Error("Schedule error: %v", err)
	}

	// <-time.After(time.Second * 20)
	// i := &NewDayTicker{taskModule: t}
	// i.Run()
}

func (t *Task) StopTick() {
	isRunning = false
}

type NewDayTicker struct {
	taskModule *Task
}

func (n *NewDayTicker) Run() {
	log.Info("run.....")

	var r bson.M
	db.COUNTER.GetCollection().FindOne(context.TODO(), &bson.M{
		"id": "server_count",
	}).Decode(&r)

	v, ok := r["seq"]
	if !ok {
		log.Error("获取服务器数量失败")
		return
	}

	// db计数
	maxServerId := cast.ToInt(v)
	if maxServerId <= 0 {
		maxServerId = 1
	}
	// 先快照, 结算的时候也从里面取排名
	n.backupPvpRank(maxServerId)
	// 获取赛季结算倒计时
	seasonKey := db.RKPvpNormalRankSeason()
	ttl := db.GetRedis().TTL(context.TODO(), seasonKey).Val()
	var wg sync.WaitGroup

	for i := 1; i <= maxServerId; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 结算每日普通奖励
			n.pvpNormalReward(i, false)
		}()
		if ttl.Seconds() > 0 {
			// 赛季没结束，不结算赛季奖励
			return
		}
		wg.Add(1)
		go func() {
			defer wg.Done()
			// 结算赛季奖励
			n.pvpNormalReward(i, true)
		}()
		// 直接删除当前赛季排行 分数有一个单独的key存放
		db.GetRedis().Del(context.TODO(), db.RKPvpNormalRank(i))
		// 赛季结算后14天再次结算
		db.GetRedis().Set(context.TODO(), seasonKey, "1", time.Hour*24*season_day)
	}

	wg.Wait()
}

// 每次结算前 先备份pvp排行 容错
func (n *NewDayTicker) backupPvpRank(maxServerId int) {
	if maxServerId <= 0 {
		return
	}
	log.Debug("结算前备份pvp排行开始...")
	var wg sync.WaitGroup
	for i := 1; i <= maxServerId; i++ {
		wg.Add(1)
		go func(i int) {
			rankKey := db.RKPvpNormalRank(i)
			for j := 1; j <= 10; j++ {
				err := db.GetRedis().ZUnionStore(context.TODO(), db.RKPvpNormalRankTodayBackup(i), &redis.ZStore{
					Keys:      []string{rankKey},
					Weights:   []float64{1}, // 设置权重为1,保持原始分数
					Aggregate: "SUM",        // 使用SUM作为聚合方式
				}).Err()
				// 这里出错 的话，只重试10次
				// TODO 超过之后 怎么处理？
				if err != nil {
					log.Error("备份pvp排行出错:%v, 区服:%d, 重试次数:%d", err, i, j)
					continue
				}
				break
			}
			// 一天过期
			db.GetRedis().PExpire(context.TODO(), db.RKPvpNormalRankTodayBackup(i), time.Hour*24)
			wg.Done()
		}(i)
	}
	wg.Wait()
	log.Debug("结算前备份pvp排行结束...")
}

// 结算普通竞技场pvp奖励
func (n *NewDayTicker) pvpNormalReward(serverId int, season bool) {
	pvp := cfg.Misc_CContainer.GetObj().Pvp
	if len(pvp) == 0 {
		return
	}
	// 普通竞技场奖励
	data := pvp[0]

	log.Debug("结算普通竞技场pvp奖励开始, 赛季:%t", season)
	// 从快照里面取
	rankKey := db.RKPvpNormalRankTodayBackup(serverId)
	total := db.GetRedis().ZCard(context.TODO(), rankKey).Val()
	log.Debug("结算普通竞技场pvp奖励, 区服:%d, 总数:%d", serverId, total)
	var sort int64 = 0
	for {
		if total <= 0 {
			break
		}

		sort += 1
		total -= 10000
		// 每次只拉 10000 个 出错重试5次
		retry := 5
		for {
			if retry <= 0 {
				break
			}
			retry--
			cmd := db.GetRedis().ZRevRange(context.TODO(), rankKey, (sort-1)*10000, (sort-1)*10000+9999)
			if e := cmd.Err(); e != nil {
				log.Error("拉取竞技场数据出错:%v, 区服:%d, 重试次数:%d", e, serverId, retry)
				continue
			}
			ary := cmd.Val()
			operations := make([]mongo.WriteModel, 0)

			for j, id := range ary {
				num := data.GetRewardNum(j + 1)
				if num <= 0 {
					continue
				}
				content := fmt.Sprintf("恭喜您在昨天的竞技场结算时排名第%d,这是您的奖励。", j+1)
				if season {
					content = fmt.Sprintf("恭喜您在上个赛季竞技场结算时排名第%d,这是您的奖励。", j+1)
					num *= 10
					// 赛季重置分数
					for retry := 0; retry < 10; retry++ {
						plrDataKey := db.RKPvpNormalDataOf(id)
						db.GetRedis().Watch(context.TODO(), func(tx *redis.Tx) error {
							r := tx.HGet(context.TODO(), plrDataKey, "score")
							if r.Err() != nil {
								return r.Err()
							}
							oldScore := cast.ToInt(cast.ToFloat64(r.Val()))
							newScore := 1000 + float64(oldScore-1000)*0.2
							tx.HSet(context.TODO(), plrDataKey, "score", newScore)
							return nil
						}, plrDataKey)
					}
				}

				operation := sendMail(n.taskModule, id, title, content, []*structs.Condition{
					{
						Type: condition.DIAMOND,
						Id:   -1,
						Num:  num,
					},
				})
				if operation != nil {
					operations = append(operations, operation)
				}
				if len(operations) >= 1000 || j == len(ary)-1 {
					_, er := db.MAIL.GetCollection().BulkWrite(context.TODO(), operations, options.BulkWrite().SetOrdered(true))
					if er != nil {
						log.Error("竞技场批量结算邮件写入出错: %v", er.Error())
					}
					operations = operations[:0]
				}
			}
			break
		}
	}

	log.Debug("结算普通竞技场pvp奖励结束, 是不是赛季:%t", season)
}

func sendMail(module *Task, id string, title, content string, rewards []*structs.Condition) mongo.WriteModel {
	// 在线玩家发放
	nodeId, err := manager.GlobalGetPlayerManager().GetPlayerNodeId(id)
	if err == nil && nodeId != "" {
		bytes, _ := json.Marshal(&rewards)
		result, estr := module.Invoke(nodeId, "/httpSendMail", id, title, content, string(bytes))
		if estr == "" && result != nil {
			if vm, ok := result.(map[string]interface{}); ok && vm != nil {
				// 发送成功!
				if code, ok := vm["code"]; ok && code.(float64) == 0 {
					return nil
				}
			}
		}
	}

	// 在线玩家发放失败 or 不在线 写入批量待更新
	mail := structs.NewMail(title, content, id, rewards)
	return mongo.NewInsertOneModel().
		SetDocument(&mail)
}
