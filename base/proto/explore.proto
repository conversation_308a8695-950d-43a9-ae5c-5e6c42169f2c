syntax = "proto3";

package proto;

option go_package = "./pb";

import "struct.proto";

//After are enums.
//After are structs.
//After are messages.
message C2S_SyncExploreMessage {
  int32 planetId = 1; //星球ID
}
message S2C_SyncExploreMessage {
  int32 code = 1; //0
  int64 surplusTime = 2; //剩余时间
}
message C2S_StartExploreMessage {
  int32 planetId = 1; //星球ID
  repeated int32 roles = 2; //派遣的角色id
}
message S2C_StartExploreMessage {
  int32 code = 1; //0
  repeated Condition rewards = 2; //探索奖励
}
message C2S_ClaimExploreRewardMessage {
  int32 planetId = 1; //星球ID
}
message S2C_ClaimExploreRewardMessage {
  int32 code = 1; //0
  int64 surplusTime = 2; //剩余时间
}
message C2S_BuySpaceshipMessage {
}
message S2C_BuySpaceshipMessage {
  int32 code = 1; //0
}
message C2S_GetExploreAreaMessage {
  int32 planetId = 1; //星球ID
}
message S2C_GetExploreAreaMessage {
  int32 code = 1; //0
  int32 area = 2; //区域
}
